-- Database functions for Supabase Edge Functions
-- These should be executed in the Supabase SQL editor

-- Function to get next document number with atomic increment
CREATE OR REPLACE FUNCTION get_next_document_number(
  p_company_id UUID,
  p_document_type TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_sequence_record RECORD;
  v_next_number INTEGER;
  v_full_number TEXT;
  v_current_year INTEGER;
  v_prefix TEXT;
BEGIN
  -- Get current year
  v_current_year := EXTRACT(YEAR FROM CURRENT_DATE);
  
  -- Lock and get sequence record
  SELECT * INTO v_sequence_record
  FROM document_sequences
  WHERE company_id = p_company_id 
    AND document_type = p_document_type
  FOR UPDATE;
  
  -- If sequence doesn't exist, create it
  IF NOT FOUND THEN
    v_prefix := UPPER(REPLACE(p_document_type, '_', '-')) || '-' || v_current_year || '-';
    
    INSERT INTO document_sequences (
      company_id,
      document_type,
      prefix,
      current_number
    ) VALUES (
      p_company_id,
      p_document_type,
      v_prefix,
      1
    );
    
    v_next_number := 1;
    v_full_number := v_prefix || LPAD(v_next_number::TEXT, 5, '0');
  ELSE
    -- Increment the current number
    v_next_number := v_sequence_record.current_number + 1;
    
    -- Update the sequence
    UPDATE document_sequences
    SET current_number = v_next_number,
        last_used_at = CURRENT_TIMESTAMP
    WHERE company_id = p_company_id 
      AND document_type = p_document_type;
    
    -- Build full number
    v_full_number := v_sequence_record.prefix || LPAD(v_next_number::TEXT, 5, '0');
  END IF;
  
  RETURN v_full_number;
END;
$$;

-- Function to detect duplicate expenses
CREATE OR REPLACE FUNCTION detect_duplicate_expense(
  p_company_id UUID,
  p_vendor_name TEXT,
  p_amount DECIMAL,
  p_expense_date DATE
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSON;
  v_existing_expense RECORD;
  v_name_similarity INTEGER;
  v_amount_similarity INTEGER;
  v_date_similarity INTEGER;
  v_total_score INTEGER;
  v_risk_level TEXT;
  v_duplicate_id UUID;
BEGIN
  -- Initialize result
  v_risk_level := 'none';
  v_duplicate_id := NULL;
  
  -- Normalize vendor name for comparison
  p_vendor_name := LOWER(TRIM(REGEXP_REPLACE(p_vendor_name, '[^a-zA-Z0-9\u0590-\u05FF ]', '', 'g')));
  
  -- Search for similar expenses within 7 days
  FOR v_existing_expense IN
    SELECT id, vendor_name, amount, expense_date
    FROM expenses
    WHERE company_id = p_company_id
      AND status != 'rejected'
      AND expense_date BETWEEN (p_expense_date - INTERVAL '7 days') AND (p_expense_date + INTERVAL '7 days')
  LOOP
    -- Calculate name similarity
    IF LOWER(TRIM(REGEXP_REPLACE(v_existing_expense.vendor_name, '[^a-zA-Z0-9\u0590-\u05FF ]', '', 'g'))) = p_vendor_name THEN
      v_name_similarity := 100;
    ELSIF LOWER(TRIM(REGEXP_REPLACE(v_existing_expense.vendor_name, '[^a-zA-Z0-9\u0590-\u05FF ]', '', 'g'))) LIKE '%' || p_vendor_name || '%' 
       OR p_vendor_name LIKE '%' || LOWER(TRIM(REGEXP_REPLACE(v_existing_expense.vendor_name, '[^a-zA-Z0-9\u0590-\u05FF ]', '', 'g'))) || '%' THEN
      v_name_similarity := 60;
    ELSE
      v_name_similarity := 0;
    END IF;
    
    -- Calculate amount similarity
    IF v_existing_expense.amount = p_amount THEN
      v_amount_similarity := 100;
    ELSIF ABS(v_existing_expense.amount - p_amount) / GREATEST(v_existing_expense.amount, p_amount) <= 0.01 THEN
      v_amount_similarity := 90;
    ELSIF ABS(v_existing_expense.amount - p_amount) / GREATEST(v_existing_expense.amount, p_amount) <= 0.05 THEN
      v_amount_similarity := 70;
    ELSE
      v_amount_similarity := 0;
    END IF;
    
    -- Calculate date similarity
    IF v_existing_expense.expense_date = p_expense_date THEN
      v_date_similarity := 100;
    ELSIF ABS(EXTRACT(DAYS FROM (v_existing_expense.expense_date - p_expense_date))) <= 1 THEN
      v_date_similarity := 80;
    ELSIF ABS(EXTRACT(DAYS FROM (v_existing_expense.expense_date - p_expense_date))) <= 3 THEN
      v_date_similarity := 60;
    ELSE
      v_date_similarity := 0;
    END IF;
    
    -- Calculate total score
    v_total_score := (v_name_similarity * 0.4 + v_amount_similarity * 0.4 + v_date_similarity * 0.2)::INTEGER;
    
    -- Determine risk level
    IF v_total_score >= 95 THEN
      v_risk_level := 'high';
      v_duplicate_id := v_existing_expense.id;
      EXIT; -- Found high risk duplicate, no need to continue
    ELSIF v_total_score >= 80 AND v_risk_level = 'none' THEN
      v_risk_level := 'low';
      v_duplicate_id := v_existing_expense.id;
    END IF;
  END LOOP;
  
  -- Build result JSON
  v_result := json_build_object(
    'risk_level', v_risk_level,
    'potential_duplicate_id', v_duplicate_id
  );
  
  RETURN v_result;
END;
$$;

-- Function to categorize expenses automatically
CREATE OR REPLACE FUNCTION categorize_expense(p_vendor_name TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_category TEXT;
  v_vendor_lower TEXT;
BEGIN
  v_vendor_lower := LOWER(p_vendor_name);
  
  -- Office supplies
  IF v_vendor_lower LIKE '%office%' OR v_vendor_lower LIKE '%משרד%' OR 
     v_vendor_lower LIKE '%paper%' OR v_vendor_lower LIKE '%נייר%' OR
     v_vendor_lower LIKE '%pen%' OR v_vendor_lower LIKE '%עט%' THEN
    v_category := 'office_supplies';
  
  -- Travel
  ELSIF v_vendor_lower LIKE '%hotel%' OR v_vendor_lower LIKE '%מלון%' OR
        v_vendor_lower LIKE '%flight%' OR v_vendor_lower LIKE '%טיסה%' OR
        v_vendor_lower LIKE '%taxi%' OR v_vendor_lower LIKE '%מונית%' OR
        v_vendor_lower LIKE '%uber%' OR v_vendor_lower LIKE '%gett%' THEN
    v_category := 'travel';
  
  -- Utilities
  ELSIF v_vendor_lower LIKE '%electric%' OR v_vendor_lower LIKE '%חשמל%' OR
        v_vendor_lower LIKE '%water%' OR v_vendor_lower LIKE '%מים%' OR
        v_vendor_lower LIKE '%gas%' OR v_vendor_lower LIKE '%גז%' OR
        v_vendor_lower LIKE '%internet%' OR v_vendor_lower LIKE '%אינטרנט%' THEN
    v_category := 'utilities';
  
  -- Professional services
  ELSIF v_vendor_lower LIKE '%lawyer%' OR v_vendor_lower LIKE '%עורך דין%' OR
        v_vendor_lower LIKE '%accountant%' OR v_vendor_lower LIKE '%רואה חשבון%' OR
        v_vendor_lower LIKE '%consultant%' OR v_vendor_lower LIKE '%יועץ%' THEN
    v_category := 'professional_services';
  
  -- Marketing
  ELSIF v_vendor_lower LIKE '%facebook%' OR v_vendor_lower LIKE '%google%' OR
        v_vendor_lower LIKE '%marketing%' OR v_vendor_lower LIKE '%שיווק%' OR
        v_vendor_lower LIKE '%advertising%' OR v_vendor_lower LIKE '%פרסום%' THEN
    v_category := 'marketing';
  
  -- Equipment
  ELSIF v_vendor_lower LIKE '%computer%' OR v_vendor_lower LIKE '%מחשב%' OR
        v_vendor_lower LIKE '%laptop%' OR v_vendor_lower LIKE '%לפטופ%' OR
        v_vendor_lower LIKE '%equipment%' OR v_vendor_lower LIKE '%ציוד%' THEN
    v_category := 'equipment';
  
  -- Default to other
  ELSE
    v_category := 'other';
  END IF;
  
  RETURN v_category;
END;
$$;

-- Function to calculate VAT report data
CREATE OR REPLACE FUNCTION calculate_vat_report(
  p_company_id UUID,
  p_period_start DATE,
  p_period_end DATE
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_sales_total DECIMAL := 0;
  v_sales_vat DECIMAL := 0;
  v_purchases_total DECIMAL := 0;
  v_purchases_vat DECIMAL := 0;
  v_result JSON;
BEGIN
  -- Calculate sales totals
  SELECT 
    COALESCE(SUM(subtotal), 0),
    COALESCE(SUM(vat_amount), 0)
  INTO v_sales_total, v_sales_vat
  FROM documents
  WHERE company_id = p_company_id
    AND document_type IN ('tax_invoice', 'tax_invoice_receipt')
    AND status IN ('approved', 'sent', 'paid')
    AND issue_date BETWEEN p_period_start AND p_period_end;
  
  -- Calculate purchases totals
  SELECT 
    COALESCE(SUM(amount), 0),
    COALESCE(SUM(vat_amount), 0)
  INTO v_purchases_total, v_purchases_vat
  FROM expenses
  WHERE company_id = p_company_id
    AND status = 'approved'
    AND expense_date BETWEEN p_period_start AND p_period_end;
  
  -- Build result
  v_result := json_build_object(
    'sales_total', v_sales_total,
    'sales_vat', v_sales_vat,
    'purchases_total', v_purchases_total,
    'purchases_vat', v_purchases_vat,
    'vat_liability', v_sales_vat - v_purchases_vat
  );
  
  RETURN v_result;
END;
$$;
