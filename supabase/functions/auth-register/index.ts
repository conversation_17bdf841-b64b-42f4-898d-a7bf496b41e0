import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  validateEmail,
  validatePassword,
  validateBusinessNumber,
  validateVatId,
  validateIsraeliPhone,
  logAudit,
  checkRateLimit,
} from '../_shared/utils.ts';
import { RegisterRequest, ValidationError } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Rate limiting
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(`register:${clientIP}`, 5, 15 * 60 * 1000)) {
      return errorResponse('Too many registration attempts. Please try again later.', 429);
    }

    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    const body: RegisterRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.email || !validateEmail(body.email)) {
      validationErrors.push({ field: 'email', message: 'Valid email is required' });
    }

    if (!body.password || !validatePassword(body.password)) {
      validationErrors.push({ 
        field: 'password', 
        message: 'Password must be at least 8 characters with 1 uppercase and 1 number' 
      });
    }

    if (!body.full_name || body.full_name.trim().length < 2) {
      validationErrors.push({ field: 'full_name', message: 'Full name is required' });
    }

    if (!body.phone || !validateIsraeliPhone(body.phone)) {
      validationErrors.push({ field: 'phone', message: 'Valid Israeli phone number is required' });
    }

    // Company validation
    if (!body.company) {
      validationErrors.push({ field: 'company', message: 'Company information is required' });
    } else {
      if (!body.company.business_number || !validateBusinessNumber(body.company.business_number)) {
        validationErrors.push({ 
          field: 'company.business_number', 
          message: 'Valid 9-digit business number is required' 
        });
      }

      if (!body.company.name_hebrew || body.company.name_hebrew.trim().length < 2) {
        validationErrors.push({ 
          field: 'company.name_hebrew', 
          message: 'Company name in Hebrew is required' 
        });
      }

      if (!body.company.vat_id || !validateVatId(body.company.vat_id)) {
        validationErrors.push({ 
          field: 'company.vat_id', 
          message: 'Valid 9-digit VAT ID is required' 
        });
      }

      if (!body.company.address_hebrew || body.company.address_hebrew.trim().length < 5) {
        validationErrors.push({ 
          field: 'company.address_hebrew', 
          message: 'Company address in Hebrew is required' 
        });
      }

      if (!body.company.city_hebrew || body.company.city_hebrew.trim().length < 2) {
        validationErrors.push({ 
          field: 'company.city_hebrew', 
          message: 'City in Hebrew is required' 
        });
      }

      if (!body.company.phone || !validateIsraeliPhone(body.company.phone)) {
        validationErrors.push({ 
          field: 'company.phone', 
          message: 'Valid Israeli phone number is required for company' 
        });
      }

      if (!body.company.industry || body.company.industry.trim().length < 2) {
        validationErrors.push({ 
          field: 'company.industry', 
          message: 'Industry is required' 
        });
      }
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Check if email already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', body.email)
      .single();

    if (existingUser) {
      return errorResponse('Email already registered', 409);
    }

    // Check if business number already exists
    const { data: existingCompany } = await supabase
      .from('companies')
      .select('id')
      .eq('business_number', body.company.business_number)
      .single();

    if (existingCompany) {
      return errorResponse('Business number already registered', 409);
    }

    // Create user account
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: body.email,
      password: body.password,
      email_confirm: true,
    });

    if (authError) {
      console.error('Auth error:', authError);
      return errorResponse('Failed to create user account', 500);
    }

    const userId = authData.user.id;

    try {
      // Create user record
      const { error: userError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: body.email,
          full_name: body.full_name,
          phone: body.phone,
        });

      if (userError) {
        throw userError;
      }

      // Create company record
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .insert({
          business_number: body.company.business_number,
          name_hebrew: body.company.name_hebrew,
          name_english: body.company.name_english,
          vat_id: body.company.vat_id,
          address_hebrew: body.company.address_hebrew,
          city_hebrew: body.company.city_hebrew,
          phone: body.company.phone,
          email: body.email,
          industry: body.company.industry,
          annual_revenue: body.company.annual_revenue,
          interested_in_loan: body.company.interested_in_loan || false,
          interested_in_insurance: body.company.interested_in_insurance || false,
          interested_in_accounting: body.company.interested_in_accounting || false,
        })
        .select()
        .single();

      if (companyError) {
        throw companyError;
      }

      // Create company-user relationship
      const { error: relationError } = await supabase
        .from('company_users')
        .insert({
          company_id: companyData.id,
          user_id: userId,
          role: 'admin',
          created_by: userId,
        });

      if (relationError) {
        throw relationError;
      }

      // Initialize document sequences
      const documentTypes = ['tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt'];
      const currentYear = new Date().getFullYear();
      
      for (const docType of documentTypes) {
        await supabase
          .from('document_sequences')
          .insert({
            company_id: companyData.id,
            document_type: docType,
            prefix: `${docType.toUpperCase()}-${currentYear}-`,
            current_number: 0,
          });
      }

      // Create session
      const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
        type: 'magiclink',
        email: body.email,
      });

      if (sessionError) {
        console.error('Session error:', sessionError);
      }

      // Log audit
      await logAudit(
        companyData.id,
        userId,
        'create',
        'user_registration',
        userId,
        null,
        { email: body.email, company_id: companyData.id },
        req
      );

      return successResponse({
        user: {
          id: userId,
          email: body.email,
          full_name: body.full_name,
          phone: body.phone,
        },
        company: companyData,
        session: sessionData,
      }, 'Registration successful');

    } catch (error) {
      // Cleanup: delete auth user if database operations failed
      await supabase.auth.admin.deleteUser(userId);
      console.error('Registration error:', error);
      return errorResponse('Registration failed. Please try again.', 500);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    return errorResponse('Internal server error', 500);
  }
});
