import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders, errorResponse, successResponse } from '../_shared/utils.ts';

interface QueueItem {
  id: string;
  document_id: string;
  status: string;
  attempts: number;
  request_payload: any;
  error_message?: string;
  next_retry_at: string;
  created_at: string;
  last_attempt_at?: string;
}

interface ProcessingResult {
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: string[];
}

function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
}

function calculateNextRetryTime(attempts: number): Date {
  // Exponential backoff: 5min, 15min, 1hr, 4hr, 12hr
  const delays = [5 * 60 * 1000, 15 * 60 * 1000, 60 * 60 * 1000, 4 * 60 * 60 * 1000, 12 * 60 * 60 * 1000];
  const delayIndex = Math.min(attempts - 1, delays.length - 1);
  const delay = delays[delayIndex];
  
  return new Date(Date.now() + delay);
}

async function submitDocumentToITA(documentId: string): Promise<{ success: boolean; allocation_number?: string; error?: string }> {
  try {
    // Call the ITA submission function
    const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/ita-submit-document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
      },
      body: JSON.stringify({ document_id: documentId })
    });

    if (response.ok) {
      const result = await response.json();
      return result.data || result;
    } else {
      const errorData = await response.text();
      return {
        success: false,
        error: `ITA submission failed: ${response.status} - ${errorData}`
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Network error: ${error.message}`
    };
  }
}

async function processQueueItem(supabase: any, item: QueueItem): Promise<{ success: boolean; error?: string }> {
  try {
    // Update status to processing
    await supabase
      .from('ita_queue')
      .update({
        status: 'processing',
        last_attempt_at: new Date().toISOString()
      })
      .eq('id', item.id);

    // Attempt ITA submission
    const result = await submitDocumentToITA(item.document_id);

    if (result.success) {
      // Mark as successful
      await supabase
        .from('ita_queue')
        .update({
          status: 'success',
          response_payload: result,
          completed_at: new Date().toISOString()
        })
        .eq('id', item.id);

      // Update document with allocation number
      await supabase
        .from('documents')
        .update({
          status: 'approved',
          ita_allocation_number: result.allocation_number,
          ita_submitted_at: new Date().toISOString()
        })
        .eq('id', item.document_id);

      return { success: true };
    } else {
      // Handle failure
      const newAttempts = item.attempts + 1;
      
      if (newAttempts >= 5) {
        // Max attempts reached - mark as failed
        await supabase
          .from('ita_queue')
          .update({
            status: 'failed',
            attempts: newAttempts,
            error_message: result.error,
            failed_at: new Date().toISOString()
          })
          .eq('id', item.id);

        // Reset document status to draft for manual intervention
        await supabase
          .from('documents')
          .update({
            status: 'draft',
            ita_error_message: result.error
          })
          .eq('id', item.document_id);

        // TODO: Notify admin of failed submission
        console.error(`ITA submission failed permanently for document ${item.document_id}: ${result.error}`);
        
        return { success: false, error: `Max attempts reached: ${result.error}` };
      } else {
        // Schedule retry
        const nextRetryAt = calculateNextRetryTime(newAttempts);
        
        await supabase
          .from('ita_queue')
          .update({
            status: 'pending',
            attempts: newAttempts,
            error_message: result.error,
            next_retry_at: nextRetryAt.toISOString()
          })
          .eq('id', item.id);

        return { success: false, error: `Retry scheduled: ${result.error}` };
      }
    }
  } catch (error) {
    // Handle unexpected errors
    await supabase
      .from('ita_queue')
      .update({
        status: 'pending',
        error_message: `Processing error: ${error.message}`
      })
      .eq('id', item.id);

    return { success: false, error: error.message };
  }
}

async function processITARetryQueue(): Promise<ProcessingResult> {
  const supabase = createSupabaseClient();
  const result: ProcessingResult = {
    processed: 0,
    successful: 0,
    failed: 0,
    skipped: 0,
    errors: []
  };

  try {
    // Get pending queue items ready for retry
    const { data: queueItems, error: fetchError } = await supabase
      .from('ita_queue')
      .select('*')
      .in('status', ['pending', 'failed'])
      .lte('next_retry_at', new Date().toISOString())
      .order('created_at', { ascending: true })
      .limit(10);

    if (fetchError) {
      result.errors.push(`Failed to fetch queue items: ${fetchError.message}`);
      return result;
    }

    if (!queueItems || queueItems.length === 0) {
      console.log('No queue items ready for processing');
      return result;
    }

    console.log(`Processing ${queueItems.length} queue items`);

    // Process each item
    for (const item of queueItems) {
      result.processed++;
      
      try {
        const itemResult = await processQueueItem(supabase, item);
        
        if (itemResult.success) {
          result.successful++;
          console.log(`Successfully processed document ${item.document_id}`);
        } else {
          result.failed++;
          result.errors.push(`Document ${item.document_id}: ${itemResult.error}`);
          console.error(`Failed to process document ${item.document_id}: ${itemResult.error}`);
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Document ${item.document_id}: ${error.message}`);
        console.error(`Error processing document ${item.document_id}:`, error);
      }
    }

    console.log(`Queue processing completed: ${result.successful} successful, ${result.failed} failed`);
    return result;
  } catch (error) {
    result.errors.push(`Queue processing error: ${error.message}`);
    console.error('Queue processing error:', error);
    return result;
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // This function can be called via cron or manually
    // No authentication required for cron jobs
    
    const result = await processITARetryQueue();

    return successResponse(result, 'Queue processing completed');
  } catch (error) {
    console.error('Process ITA queue error:', error);
    return errorResponse('Internal server error', 500);
  }
});
