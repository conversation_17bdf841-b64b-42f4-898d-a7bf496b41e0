

Excellent! I now have all the requirements needed. Let me create an exhaustive technical specification for your Israeli B2B invoicing application.

# **Complete Technical Specification - Israeli B2B Invoicing Application**

## **Part 1: Database Architecture**

### **Core Business Tables**

```sql
-- Companies Table (Multi-tenant root)
Table: companies
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- business_number: varchar(9) | unique, not null | Israeli business registration (ח.פ/ע.מ)
- name_hebrew: varchar(255) | not null
- name_english: varchar(255) | nullable
- vat_id: varchar(9) | not null | VAT registration number
- address_hebrew: text | not null
- address_english: text | nullable
- city_hebrew: varchar(100) | not null
- city_english: varchar(100) | nullable
- postal_code: varchar(7) | nullable
- phone: varchar(15) | not null
- email: varchar(255) | not null
- logo_url: text | nullable
- subscription_tier: enum('free', 'paid') | default: 'free'
- subscription_expires_at: timestamp | nullable
- industry: varchar(100) | not null
- annual_revenue: varchar(50) | nullable
- interested_in_loan: boolean | default: false
- interested_in_insurance: boolean | default: false
- interested_in_accounting: boolean | default: false
- created_at: timestamp | default: now()
- updated_at: timestamp | default: now()

Indexes:
- idx_companies_business_number ON (business_number)
- idx_companies_subscription ON (subscription_tier, subscription_expires_at)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = companies.id)
- INSERT: false (only through signup function)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = companies.id AND role = 'admin')
- DELETE: false
```

```sql
-- Users Management
Table: users
Fields:
- id: uuid | primary key | references auth.users(id)
- email: varchar(255) | unique, not null
- full_name: varchar(255) | not null
- phone: varchar(15) | nullable
- created_at: timestamp | default: now()
- last_login_at: timestamp | nullable

Table: company_users
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- user_id: uuid | references users(id) | not null
- role: enum('admin', 'user', 'accountant') | not null
- created_at: timestamp | default: now()
- created_by: uuid | references users(id)

Indexes:
- idx_company_users_unique ON (company_id, user_id) UNIQUE
- idx_company_users_role ON (company_id, role)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = company_users.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = company_users.company_id AND role = 'admin')
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = company_users.company_id AND role = 'admin')
- DELETE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = company_users.company_id AND role = 'admin')
```

### **Document Management Tables**

```sql
-- Document Sequences (for sequential numbering compliance)
Table: document_sequences
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- document_type: enum('tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt') | not null
- prefix: varchar(20) | nullable | e.g., "INV-2025-"
- current_number: integer | not null | default: 0
- last_used_at: timestamp | nullable
- created_at: timestamp | default: now()

Indexes:
- idx_sequences_unique ON (company_id, document_type) UNIQUE

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = document_sequences.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = document_sequences.company_id AND role = 'admin')
- UPDATE: false (only through functions to ensure atomicity)
```

```sql
-- Customers Table
Table: customers
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- business_number: varchar(9) | not null
- name_hebrew: varchar(255) | not null
- name_english: varchar(255) | nullable
- vat_id: varchar(9) | nullable
- billing_address_hebrew: text | not null
- billing_address_english: text | nullable
- shipping_address_hebrew: text | nullable
- shipping_address_english: text | nullable
- city_hebrew: varchar(100) | not null
- city_english: varchar(100) | nullable
- contact_name: varchar(255) | nullable
- contact_email: varchar(255) | nullable
- contact_phone: varchar(15) | nullable
- notes: text | nullable
- created_at: timestamp | default: now()
- updated_at: timestamp | default: now()

Indexes:
- idx_customers_company ON (company_id)
- idx_customers_business_number ON (company_id, business_number)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = customers.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = customers.company_id)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = customers.company_id)
- DELETE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = customers.company_id AND role IN ('admin', 'accountant'))
```

```sql
-- Products/Services Catalog
Table: products
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- sku: varchar(100) | nullable
- name_hebrew: varchar(255) | not null
- name_english: varchar(255) | nullable
- description_hebrew: text | nullable
- description_english: text | nullable
- unit_price: decimal(10,2) | not null
- currency: varchar(3) | default: 'ILS'
- vat_rate: decimal(4,2) | default: 18.00
- is_service: boolean | default: false
- is_active: boolean | default: true
- created_at: timestamp | default: now()
- updated_at: timestamp | default: now()

Indexes:
- idx_products_company ON (company_id)
- idx_products_sku ON (company_id, sku) WHERE sku IS NOT NULL

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = products.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = products.company_id)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = products.company_id)
- DELETE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = products.company_id AND role IN ('admin', 'accountant'))
```

### **Invoice and Document Tables**

```sql
-- Main Documents Table
Table: documents
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- document_type: enum('tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt') | not null
- document_number: varchar(50) | not null | Sequential number with prefix
- customer_id: uuid | references customers(id) | not null
- issue_date: date | not null
- due_date: date | nullable
- currency: varchar(3) | default: 'ILS'
- subtotal: decimal(12,2) | not null
- vat_amount: decimal(10,2) | not null
- total_amount: decimal(12,2) | not null
- status: enum('draft', 'pending_allocation', 'approved', 'sent', 'paid', 'cancelled') | default: 'draft'
- ita_allocation_number: varchar(50) | nullable | ITA SHAAM allocation number
- ita_allocation_date: timestamp | nullable
- ita_submission_attempts: integer | default: 0
- ita_last_error: text | nullable
- parent_document_id: uuid | references documents(id) | nullable | For credit notes
- notes: text | nullable
- template_id: varchar(50) | default: 'default'
- pdf_url: text | nullable
- sent_at: timestamp | nullable
- sent_via: varchar(20) | nullable | 'email', 'whatsapp'
- created_by: uuid | references users(id) | not null
- created_at: timestamp | default: now()
- updated_at: timestamp | default: now()

Indexes:
- idx_documents_company ON (company_id)
- idx_documents_number ON (company_id, document_number) UNIQUE
- idx_documents_customer ON (customer_id)
- idx_documents_status ON (company_id, status)
- idx_documents_date ON (company_id, issue_date DESC)
- idx_documents_allocation ON (ita_allocation_number) WHERE ita_allocation_number IS NOT NULL

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = documents.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = documents.company_id)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = documents.company_id)
- DELETE: false (soft delete only through status = 'cancelled')
```

```sql
-- Document Line Items
Table: document_items
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- document_id: uuid | references documents(id) ON DELETE CASCADE | not null
- product_id: uuid | references products(id) | nullable
- line_number: integer | not null
- description_hebrew: text | not null
- description_english: text | nullable
- quantity: decimal(10,3) | not null
- unit_price: decimal(10,2) | not null
- currency: varchar(3) | default: 'ILS'
- discount_percent: decimal(5,2) | default: 0
- vat_rate: decimal(4,2) | default: 18.00
- line_total: decimal(10,2) | not null
- vat_amount: decimal(10,2) | not null
- total_with_vat: decimal(10,2) | not null

Indexes:
- idx_document_items_document ON (document_id, line_number)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users cu JOIN documents d ON d.company_id = cu.company_id WHERE d.id = document_items.document_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users cu JOIN documents d ON d.company_id = cu.company_id WHERE d.id = document_items.document_id)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users cu JOIN documents d ON d.company_id = cu.company_id WHERE d.id = document_items.document_id)
- DELETE: auth.uid() IN (SELECT user_id FROM company_users cu JOIN documents d ON d.company_id = cu.company_id WHERE d.id = document_items.document_id)
```

```sql
-- Document Relationships (for linking invoices to receipts)
Table: document_relationships
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- source_document_id: uuid | references documents(id) | not null
- target_document_id: uuid | references documents(id) | not null
- relationship_type: enum('invoice_to_receipt', 'invoice_to_credit') | not null
- amount_applied: decimal(10,2) | not null
- created_at: timestamp | default: now()

Indexes:
- idx_relationships_source ON (source_document_id)
- idx_relationships_target ON (target_document_id)
- idx_relationships_unique ON (source_document_id, target_document_id) UNIQUE
```

### **Expense Management Tables**

```sql
-- Email Accounts for Scanning
Table: email_accounts
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- email_address: varchar(255) | not null
- provider: enum('gmail', 'outlook') | not null
- access_token: text | encrypted | not null
- refresh_token: text | encrypted | nullable
- token_expires_at: timestamp | nullable
- last_sync_at: timestamp | nullable
- is_active: boolean | default: true
- created_at: timestamp | default: now()

Indexes:
- idx_email_accounts_company ON (company_id)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = email_accounts.company_id AND role IN ('admin', 'accountant'))
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = email_accounts.company_id AND role = 'admin')
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = email_accounts.company_id AND role = 'admin')
- DELETE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = email_accounts.company_id AND role = 'admin')
```

```sql
-- Expenses Table
Table: expenses
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- expense_number: varchar(50) | not null
- vendor_name: varchar(255) | not null
- expense_date: date | not null
- amount: decimal(10,2) | not null
- vat_amount: decimal(10,2) | not null
- total_amount: decimal(10,2) | not null
- currency: varchar(3) | default: 'ILS'
- category: enum('office_supplies', 'travel', 'utilities', 'rent', 'professional_services', 'marketing', 'equipment', 'other') | not null
- description: text | nullable
- status: enum('pending', 'approved', 'rejected') | default: 'pending'
- duplicate_risk: enum('none', 'low', 'high') | default: 'none'
- duplicate_of_id: uuid | references expenses(id) | nullable
- source: enum('manual', 'email_scan', 'upload') | not null
- source_email_id: uuid | references email_accounts(id) | nullable
- original_file_url: text | nullable
- extracted_data: jsonb | nullable | OCR/AI extracted data
- approved_by: uuid | references users(id) | nullable
- approved_at: timestamp | nullable
- rejection_reason: text | nullable
- created_at: timestamp | default: now()
- updated_at: timestamp | default: now()

Indexes:
- idx_expenses_company ON (company_id)
- idx_expenses_status ON (company_id, status)
- idx_expenses_date ON (company_id, expense_date DESC)
- idx_expenses_duplicate ON (company_id, duplicate_risk) WHERE duplicate_risk != 'none'

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = expenses.company_id)
- INSERT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = expenses.company_id)
- UPDATE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = expenses.company_id AND role IN ('admin', 'accountant'))
- DELETE: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = expenses.company_id AND role = 'accountant')
```

### **System Tables**

```sql
-- Audit Log
Table: audit_log
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- company_id: uuid | references companies(id) | not null
- user_id: uuid | references users(id) | not null
- action: varchar(50) | not null | 'create', 'update', 'delete', 'approve', etc.
- entity_type: varchar(50) | not null | 'document', 'expense', 'customer', etc.
- entity_id: uuid | not null
- old_values: jsonb | nullable
- new_values: jsonb | nullable
- ip_address: inet | nullable
- user_agent: text | nullable
- created_at: timestamp | default: now()

Indexes:
- idx_audit_company ON (company_id, created_at DESC)
- idx_audit_entity ON (entity_type, entity_id)

RLS Rules:
- SELECT: auth.uid() IN (SELECT user_id FROM company_users WHERE company_id = audit_log.company_id AND role IN ('admin', 'accountant'))
- INSERT: false (only through triggers)
```

```sql
-- ITA Queue for retry logic
Table: ita_queue
Fields:
- id: uuid | primary key | default: gen_random_uuid()
- document_id: uuid | references documents(id) | not null
- status: enum('pending', 'processing', 'success', 'failed') | default: 'pending'
- attempts: integer | default: 0
- next_retry_at: timestamp | nullable
- request_payload: jsonb | not null
- response_payload: jsonb | nullable
- error_message: text | nullable
- created_at: timestamp | default: now()
- processed_at: timestamp | nullable

Indexes:
- idx_ita_queue_status ON (status, next_retry_at) WHERE status IN ('pending', 'processing')
```

## **Part 2: API Endpoints Specification**

### **Authentication Endpoints**

```typescript
// User Registration
POST /api/auth/register
Request: {
  email: string,
  password: string,
  full_name: string,
  phone: string,
  company: {
    business_number: string,
    name_hebrew: string,
    name_english?: string,
    vat_id: string,
    address_hebrew: string,
    city_hebrew: string,
    phone: string,
    industry: string,
    annual_revenue?: string,
    interested_in_loan: boolean,
    interested_in_insurance: boolean,
    interested_in_accounting: boolean
  }
}
Response: {
  user: User,
  company: Company,
  session: Session
}
Validation:
- Email format validation
- Password min 8 chars, 1 uppercase, 1 number
- Business number: 9 digits
- VAT ID: 9 digits
Error Codes:
- 400: Invalid input
- 409: Email/business already exists
- 500: Server error
```

```typescript
// Login
POST /api/auth/login
Request: {
  email: string,
  password: string
}
Response: {
  user: User,
  companies: Company[], // User might belong to multiple companies
  session: Session
}
```

### **Document Management Endpoints**

```typescript
// Get next document number
GET /api/documents/next-number
Query: {
  company_id: uuid,
  document_type: 'tax_invoice' | 'receipt' | 'credit_note' | 'tax_invoice_receipt'
}
Response: {
  next_number: string, // e.g., "INV-2025-0001"
  prefix: string,
  sequence: number
}
```

```typescript
// Create document (with ITA integration)
POST /api/documents
Request: {
  company_id: uuid,
  document_type: string,
  customer_id: uuid,
  issue_date: string, // YYYY-MM-DD
  due_date?: string,
  currency: string,
  items: [{
    product_id?: uuid,
    description_hebrew: string,
    description_english?: string,
    quantity: number,
    unit_price: number,
    vat_rate: number,
    discount_percent?: number
  }],
  notes?: string,
  template_id?: string
}
Response: {
  document: Document,
  ita_submission: {
    status: 'queued' | 'success' | 'failed',
    allocation_number?: string,
    error?: string
  }
}
Business Logic:
1. Validate customer exists
2. Get next sequential number
3. Calculate totals and VAT
4. Create document in 'pending_allocation' status
5. Submit to ITA SHAAM API
6. Update with allocation number or queue for retry
7. Generate PDF
8. Return document
```

```typescript
// ITA SHAAM Integration
POST /api/ita/submit-document
Internal Endpoint - Called by document creation
Request: {
  document_id: uuid
}
Process:
1. Fetch document with all relations
2. Format according to ITA JSON schema:
   {
     "DocumentType": "חשבונית מס",
     "DocumentNumber": string,
     "IssueDate": "YYYY-MM-DD",
     "SupplierVatId": string,
     "CustomerVatId": string,
     "Items": [...],
     "TotalBeforeVat": number,
     "VatAmount": number,
     "TotalAmount": number
   }
3. Sign with digital certificate
4. Submit to SHAAM API
5. Handle response:
   - Success: Update document with allocation number
   - Failure: Queue for retry with exponential backoff
```

```typescript
// Send document
POST /api/documents/{id}/send
Request: {
  method: 'email' | 'whatsapp',
  recipient_email?: string,
  recipient_phone?: string
}
Response: {
  status: 'sent',
  sent_at: timestamp,
  delivery_method: string
}
```

### **Customer Management Endpoints**

```typescript
// Create customer
POST /api/customers
Request: {
  company_id: uuid,
  business_number: string,
  name_hebrew: string,
  name_english?: string,
  vat_id?: string,
  billing_address_hebrew: string,
  city_hebrew: string,
  contact_email?: string,
  contact_phone?: string
}
Response: Customer

// Search customers
GET /api/customers/search
Query: {
  company_id: uuid,
  query: string // searches name, business number
}
Response: Customer[]
```

### **Expense Management Endpoints**

```typescript
// Email account connection
POST /api/email-accounts/connect
Request: {
  company_id: uuid,
  provider: 'gmail' | 'outlook',
  auth_code: string // OAuth code from provider
}
Process:
1. Exchange auth code for tokens
2. Store encrypted tokens
3. Trigger initial scan
Response: EmailAccount

// Manual expense scan trigger
POST /api/expenses/scan
Request: {
  company_id: uuid,
  email_account_id?: uuid // optional, scans all if not provided
}
Response: {
  scanned_count: number,
  new_expenses: number,
  duplicates_detected: number
}

// Approve/Reject expense
PATCH /api/expenses/{id}/status
Request: {
  status: 'approved' | 'rejected',
  rejection_reason?: string
}
Response: Expense
Authorization: Must be accountant role
```

### **Reporting Endpoints**

```typescript
// VAT Report
GET /api/reports/vat
Query: {
  company_id: uuid,
  period_start: string, // YYYY-MM-DD
  period_end: string
}
Response: {
  period: string,
  sales: {
    total_before_vat: number,
    vat_collected: number,
    total_with_vat: number,
    documents: DocumentSummary[]
  },
  purchases: {
    total_before_vat: number,
    vat_paid: number,
    total_with_vat: number,
    expenses: ExpenseSummary[]
  },
  vat_liability: number, // vat_collected - vat_paid
  export_formats: {
    pdf_url: string,
    excel_url: string,
    pcn874_url: string
  }
}
```

## **Part 3: Page Specifications**

### **1. Landing Page (Existing Design Reference)**

```
Page: Landing
Route: /
Components: DESIGN_SYSTEM.md
Design Tokens:
Referenced in DESIGN_SYSTEM.md
```

### **2. Authentication Pages**

```
Page: Register
Route: /register
Layout: Center-aligned card (600px max-width)

Components:
- ProgressIndicator:
  Position: top of card
  Steps: ['Account', 'Business', 'Survey']
  
- Step1_AccountForm:
  Fields:
    - email: input | type="email" | required
    - password: input | type="password" | required | strength indicator
    - full_name: input | required
    - phone: input | type="tel" | required
  Actions:
    - onNext: validate and proceed to step 2
    
- Step2_BusinessForm:
  Fields:
    - business_number: input | pattern="[0-9]{9}" | required
    - name_hebrew: input | required | RTL
    - name_english: input | optional
    - vat_id: input | pattern="[0-9]{9}" | required
    - address_hebrew: textarea | required | RTL
    - city_hebrew: input | required | RTL
    - phone: input | type="tel" | required
  Actions:
    - onBack: return to step 1
    - onNext: proceed to step 3
    
- Step3_SurveyForm:
  Fields:
    - industry: select | required
      Options: ['Technology', 'Retail', 'Services', 'Manufacturing', 'Construction', 'Healthcare', 'Other']
    - annual_revenue: select | optional
      Options: ['< 500K', '500K-1M', '1M-5M', '5M-10M', '10M+']
    - interested_in_loan: checkbox
    - interested_in_insurance: checkbox
    - interested_in_accounting: checkbox
  Actions:
    - onBack: return to step 2
    - onSubmit: create account and company
    
API Calls:
- POST /api/auth/register on final submit
- On success: redirect to /dashboard
```

### **3. Main Dashboard**

```
Page: Dashboard
Route: /dashboard
Layout: Sidebar navigation + main content area

Components:
- Sidebar:
  Position: left, 260px width, full height
  Background: #F8F9FA
  Items:
    - Logo and Company Name
    - Navigation:
      - Dashboard (icon: grid)
      - Documents (icon: file-text)
        - Invoices
        - Receipts
        - Credit Notes
      - Customers (icon: users)
      - Products (icon: package)
      - Expenses (icon: credit-card) [badge: pending count]
      - Reports (icon: bar-chart)
      - Settings (icon: settings)
    - User Menu (bottom):
      - User name and role
      - Logout

- MainContent:
  Position: flex-1, padding: 24px
  
  - StatsCards:
    Position: grid, 4 columns
    Cards:
      - This Month Revenue:
        Value: sum of approved invoices
        Change: vs last month %
      - Pending VAT:
        Value: calculated VAT liability
        Subtitle: Next payment date
      - Open Invoices:
        Value: count of unpaid invoices
        Subtitle: Total amount
      - Pending Expenses:
        Value: count of pending expenses
        Action: Review → /expenses
        
  - RecentDocuments:
    Position: below stats, margin-top: 24px
    Table columns:
      - Document # | Type | Customer | Amount | Status | Actions
    Actions:
      - View PDF
      - Send
      - Edit (if draft)
    
  - QuickActions:
    Position: floating button, bottom-right
    Options:
      - New Invoice
      - New Receipt
      - New Customer
      - Scan Expense
```

### **4. Document Creation Wizard**

```
Page: CreateDocument
Route: /documents/create?type={type}
Layout: Full screen with step indicator

Components:
- StepIndicator:
  Position: top, below header
  Steps: ['Customer', 'Items', 'Review', 'Send']
  
- Step1_CustomerSelection:
  - SearchBar:
    Position: top
    Placeholder: "Search by name or business number"
    onSearch: GET /api/customers/search
    
  - CustomerList:
    Position: below search
    Layout: cards grid
    EmptyState: "No customers found. Create new?"
    
  - CreateCustomerModal:
    Trigger: "Add New Customer" button
    Fields: [same as customer creation form]
    onSave: POST /api/customers
    
- Step2_ItemsEntry:
  - ItemsTable:
    Columns:
      - Description (hebrew) | Quantity | Price | VAT | Total
    Actions:
      - Add Row
      - Delete Row
      - Product Picker (opens modal)
      
  - ProductPickerModal:
    SearchableList of products
    onSelect: populate row with product details
    
  - Totals:
    Position: bottom-right
    Display:
      - Subtotal
      - VAT (18%)
      - Total
      
- Step3_Review:
  - DocumentPreview:
    Position: center, A4 size
    Template: selected template design
    Live preview of document
    
  - EditableFields:
    - Issue Date
    - Due Date (if invoice)
    - Notes
    - Template Selection
    
- Step4_Send:
  - DeliveryOptions:
    - Email:
      Fields: recipient email (pre-filled)
      Preview: email template
    - WhatsApp:
      Fields: recipient phone
      Action: opens WhatsApp with pre-filled message
    - Download PDF:
      Action: immediate download
      
API Calls:
- GET /api/documents/next-number on mount
- POST /api/documents on final submit
- POST /api/documents/{id}/send on delivery
```

### **5. Expense Management Page**

```
Page: Expenses
Route: /expenses
Layout: Split view - list and details

Components:
- FilterBar:
  Position: top
  Filters:
    - Status: ['All', 'Pending', 'Approved', 'Rejected']
    - Date Range: date picker
    - Category: dropdown
    - Duplicate Risk: ['All', 'High Risk', 'Low Risk']
    
- ExpenseList:
  Position: left, 40% width
  Layout: scrollable list
  Item Display:
    - Vendor Name (bold)
    - Amount and Date
    - Status Badge
    - Duplicate Warning (if applicable)
    
- ExpenseDetails:
  Position: right, 60% width
  Sections:
    - Document Preview:
      If available: show PDF/image
      Else: "No document attached"
    - Extracted Data:
      Editable fields from OCR
    - Actions (for accountants only):
      - Approve: PATCH /api/expenses/{id}/status
      - Reject: opens rejection reason modal
      - Mark as Duplicate
      
- EmailAccountsModal:
  Trigger: "Connect Email" button
  Content:
    - Provider Selection (Gmail/Outlook)
    - OAuth Flow
    - Connected Accounts List
    - Remove Account option
```

### **6. Reports Page**

```
Page: Reports
Route: /reports
Layout: Tabs for different report types

Components:
- ReportTabs:
  Tabs:
    - VAT Report
    - Income Statement
    - Customer Summary
    
- VATReportTab:
  - PeriodSelector:
    Default: current bi-monthly period
    Custom range option
    
  - VATSummary:
    Cards:
      - Sales VAT Collected
      - Purchase VAT Paid
      - Net VAT Liability
      - Payment Due Date
      
  - DetailsTables:
    - Sales Documents table
    - Purchase Expenses table
    
  - ExportActions:
    - Download PDF
    - Download Excel
    - Export PCN874 (for accountant)
    
API Calls:
- GET /api/reports/vat with period params
```

## **Part 4: Business Logic Functions**


### **Sequential Number Generation**

```typescript
Function: getNextDocumentNumber
Purpose: Generate next sequential number for document with atomicity guarantee
Input: {
  company_id: uuid,
  document_type: 'tax_invoice' | 'receipt' | 'credit_note' | 'tax_invoice_receipt'
}
Output: {
  full_number: string,
  sequence_number: number,
  prefix: string
}
Logic:
1. BEGIN TRANSACTION
2. SELECT * FROM document_sequences 
   WHERE company_id = {company_id} 
   AND document_type = {document_type}
   FOR UPDATE
3. IF not exists:
   INSERT new sequence with current_number = 0
4. INCREMENT current_number by 1
5. UPDATE last_used_at = now()
6. Format number:
   - If prefix exists: {prefix}{padded_number}
   - Else: {document_type_code}-{year}-{padded_number}
   - Padding: 5 digits (00001)
7. COMMIT TRANSACTION
8. Return formatted number
Edge Cases:
- Concurrent requests: handled by FOR UPDATE lock
- Missing sequence: auto-create with defaults
- Year change: prefix includes year
```

### **ITA SHAAM Integration**

```typescript
Function: submitDocumentToITA
Purpose: Submit document to Israeli Tax Authority SHAAM system
Input: {
  document_id: uuid
}
Output: {
  success: boolean,
  allocation_number?: string,
  error?: string
}
Logic:
1. Fetch document with all relations:
   - Company details
   - Customer details
   - Line items
2. Validate document readiness:
   - Status must be 'pending_allocation'
   - All required fields present
   - Customer has valid VAT ID
3. Build ITA JSON payload:
   {
     "MessageType": "Invoice",
     "MessageVersion": "1.0",
     "UniqueID": document.id,
     "Supplier": {
       "VatID": company.vat_id,
       "Name": company.name_hebrew,
       "Address": company.address_hebrew
     },
     "Customer": {
       "VatID": customer.vat_id,
       "Name": customer.name_hebrew,
       "Address": customer.billing_address_hebrew
     },
     "DocumentDetails": {
       "DocumentType": mapDocumentType(document.document_type),
       "DocumentNumber": document.document_number,
       "IssueDate": document.issue_date,
       "Currency": document.currency,
       "ExchangeRate": 1.0 // If ILS
     },
     "Items": document_items.map(item => ({
       "Description": item.description_hebrew,
       "Quantity": item.quantity,
       "UnitPrice": item.unit_price,
       "VATRate": item.vat_rate,
       "LineTotal": item.line_total,
       "VATAmount": item.vat_amount
     })),
     "Totals": {
       "TotalBeforeVAT": document.subtotal,
       "TotalVAT": document.vat_amount,
       "TotalAmount": document.total_amount
     }
   }
4. Sign payload with digital certificate:
   - Load certificate from secure storage
   - Create PKCS#7 signature
   - Encode as base64
5. Submit to ITA API:
   POST https://api.taxes.gov.il/shaam/v1/documents
   Headers:
     - Authorization: Bearer {api_token}
     - Content-Type: application/json
     - X-Digital-Signature: {signature}
   Body: {signed_payload}
6. Handle response:
   - Success (200):
     * Extract allocation_number
     * Update document status = 'approved'
     * Store allocation details
   - Client Error (400):
     * Log error details
     * Update document with error
     * Return for user correction
   - Server Error (500):
     * Queue for retry
     * Increment attempt counter
7. Return result
Edge Cases:
- Network timeout: Queue for retry
- Invalid certificate: Alert admin
- Malformed response: Log and retry
- Duplicate submission: Check allocation exists first
```

### **Retry Queue Processing**

```typescript
Function: processITARetryQueue
Purpose: Process failed ITA submissions with exponential backoff
Schedule: Run every 5 minutes via cron
Logic:
1. SELECT * FROM ita_queue 
   WHERE status IN ('pending', 'failed')
   AND next_retry_at <= now()
   ORDER BY created_at
   LIMIT 10
2. FOR each queue item:
   a. Set status = 'processing'
   b. Call submitDocumentToITA(document_id)
   c. IF success:
      - Update status = 'success'
      - Update document with allocation
   d. IF failure:
      - Increment attempts
      - IF attempts < 5:
        * Calculate next_retry_at:
          - Attempt 1: 5 minutes
          - Attempt 2: 15 minutes
          - Attempt 3: 1 hour
          - Attempt 4: 4 hours
          - Attempt 5: 12 hours
        * Set status = 'pending'
      - ELSE:
        * Set status = 'failed'
        * Notify admin
        * Update document status = 'draft'
3. Log all processing results
```

### **VAT Calculation**

```typescript
Function: calculateDocumentTotals
Purpose: Calculate all monetary values for document
Input: {
  items: [{
    quantity: number,
    unit_price: number,
    discount_percent: number,
    vat_rate: number
  }],
  currency: string
}
Output: {
  subtotal: number,
  total_discount: number,
  vat_amount: number,
  total_amount: number,
  items_calculated: [{...item, calculations}]
}
Logic:
1. FOR each item:
   a. Calculate base_amount = quantity * unit_price
   b. Calculate discount_amount = base_amount * (discount_percent / 100)
   c. Calculate line_total = base_amount - discount_amount
   d. Calculate vat_amount = line_total * (vat_rate / 100)
   e. Calculate total_with_vat = line_total + vat_amount
   f. Round all values to 2 decimal places
2. Calculate document totals:
   - subtotal = SUM(all line_totals)
   - total_discount = SUM(all discount_amounts)
   - vat_amount = SUM(all vat_amounts)
   - total_amount = subtotal + vat_amount
3. Apply rounding rules:
   - Individual lines: round to 2 decimals
   - Document totals: round to 2 decimals
   - Ensure: SUM(line_totals) = document.subtotal
4. Return all calculations
Edge Cases:
- Zero VAT for exports: Check if customer.country != 'IL'
- Mixed VAT rates: Calculate each line separately
- Negative amounts: Allow for credit notes
```

### **Email Scanning AI Processing**

```typescript
Function: scanEmailForExpenses
Purpose: Scan email inbox for invoices and receipts using AI
Input: {
  email_account_id: uuid,
  since_date?: timestamp
}
Output: {
  scanned_count: number,
  new_expenses: number,
  duplicates: number
}
Logic:
1. Fetch email account credentials:
   - Decrypt access_token
   - Check token expiry, refresh if needed
2. Connect to email provider:
   - Gmail: Use Gmail API
   - Outlook: Use Microsoft Graph API
3. Query emails:
   - Filter: has attachments
   - Date: since last_sync_at or since_date
   - Limit: 100 per batch
4. FOR each email:
   a. Download attachments
   b. Filter relevant files:
      - PDF files
      - Images (JPG, PNG)
      - Skip if > 10MB
   c. FOR each attachment:
      i. Upload to Supabase Storage
      ii. Process with OCR/AI:
         - Extract text using Tesseract (Hebrew + English)
         - Send to OpenAI API:
           ```
           Prompt: "Extract invoice/receipt data from this text.
           Return JSON with:
           - vendor_name (Hebrew or English)
           - date (YYYY-MM-DD)
           - invoice_number
           - amount_before_vat
           - vat_amount
           - total_amount
           - currency
           - is_invoice (boolean)
           If not an invoice/receipt, return {is_invoice: false}"
           ```
      iii. IF is_invoice:
         - Check for duplicates:
           * Same vendor + amount + date = high risk
           * Same vendor + amount = low risk
         - Create expense record:
           ```sql
           INSERT INTO expenses (
             company_id,
             vendor_name,
             expense_date,
             amount,
             vat_amount,
             total_amount,
             currency,
             category: auto_categorize(vendor_name),
             status: 'pending',
             duplicate_risk,
             source: 'email_scan',
             source_email_id,
             original_file_url,
             extracted_data: {ai_response}
           )
           ```
5. Update email_account.last_sync_at
6. Return statistics
Edge Cases:
- Corrupted files: Skip and log
- OCR failure: Store raw file for manual review
- AI extraction failure: Create with minimal data
- Rate limits: Implement backoff
```

### **Duplicate Detection**

```typescript
Function: detectDuplicateExpense
Purpose: Identify potential duplicate expenses
Input: {
  company_id: uuid,
  vendor_name: string,
  amount: number,
  expense_date: date
}
Output: {
  risk_level: 'none' | 'low' | 'high',
  potential_duplicate_id?: uuid
}
Logic:
1. Normalize vendor_name:
   - Remove special characters
   - Convert to lowercase
   - Remove common suffixes (Ltd, Inc, בע"מ)
2. Query existing expenses:
   ```sql
   SELECT * FROM expenses
   WHERE company_id = {company_id}
   AND expense_date BETWEEN {date - 7 days} AND {date + 7 days}
   AND status != 'rejected'
   ```
3. Calculate similarity scores:
   FOR each existing expense:
   a. Name similarity:
      - Exact match: 100
      - Levenshtein distance < 3: 80
      - Contains substring: 60
   b. Amount similarity:
      - Exact match: 100
      - Within 1%: 90
      - Within 5%: 70
   c. Date similarity:
      - Same date: 100
      - Within 1 day: 80
      - Within 3 days: 60
   d. Total score = (name * 0.4) + (amount * 0.4) + (date * 0.2)
4. Determine risk level:
   - Score >= 95: 'high' risk
   - Score >= 80: 'low' risk
   - Score < 80: 'none'
5. Return highest risk match
```

### **Document PDF Generation**

```typescript
Function: generateDocumentPDF
Purpose: Generate PDF for any document type
Input: {
  document_id: uuid,
  template_id: string
}
Output: {
  pdf_url: string,
  pdf_size: number
}
Logic:
1. Fetch complete document data:
   - Document details
   - Company info + logo
   - Customer info
   - Line items
2. Load template configuration:
   Templates = {
     'modern': { colors: {...}, fonts: {...}, layout: {...} },
     'classic': { ... },
     'minimal': { ... }
   }
3. Generate HTML structure:
   ```html
   <div class="invoice-container">
     <header>
       <img src="{logo}" />
       <div class="company-info">...</div>
       <div class="document-title">{type_hebrew}</div>
     </header>
     <section class="parties">
       <div class="from">מאת: {company}</div>
       <div class="to">עבור: {customer}</div>
     </section>
     <section class="items">
       <table>...</table>
     </section>
     <footer>
       <div class="totals">...</div>
       <div class="allocation">מספר אישור: {allocation}</div>
     </footer>
   </div>
   ```
4. Apply RTL for Hebrew content:
   - Set dir="rtl" for Hebrew sections
   - Use appropriate fonts (Heebo, Arial)
5. Convert HTML to PDF:
   - Use Puppeteer or similar
   - A4 size, 1.5cm margins
   - Embed fonts
6. Add QR code (if payment enabled):
   - Generate QR with document URL
   - Position bottom-right
7. Upload to Supabase Storage:
   - Path: /documents/{company_id}/{year}/{document_number}.pdf
   - Set public read access
8. Update document.pdf_url
9. Return URL
```

### **WhatsApp Message Builder**

```typescript
Function: buildWhatsAppMessage
Purpose: Create pre-formatted WhatsApp message with document link
Input: {
  document_id: uuid,
  recipient_phone: string
}
Output: {
  whatsapp_url: string,
  message_text: string
}
Logic:
1. Fetch document basic info
2. Generate public share link:
   - Create signed URL (valid for 30 days)
   - Short URL using bit.ly API
3. Build message text:
   ```
   שלום,
   
   מצורף {document_type} מספר {document_number}
   מתאריך {issue_date}
   על סך {total_amount} {currency}
   
   לצפייה והורדה:
   {short_url}
   
   תודה,
   {company_name}
   ```
4. Encode message for URL
5. Build WhatsApp URL:
   - Mobile: whatsapp://send?phone={phone}&text={encoded_message}
   - Web: https://wa.me/{phone}?text={encoded_message}
6. Return both URL and plain text
```

## **Part 5: Security Implementation**

### **Row Level Security Policies**

```sql
-- Companies RLS
CREATE POLICY companies_select ON companies
  FOR SELECT USING (
    id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY companies_update ON companies
  FOR UPDATE USING (
    id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Documents RLS with status-based access
CREATE POLICY documents_insert ON documents
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY documents_update ON documents
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid()
    )
    AND (
      status = 'draft' 
      OR auth.uid() IN (
        SELECT user_id FROM company_users 
        WHERE company_id = documents.company_id 
        AND role IN ('admin', 'accountant')
      )
    )
  );

-- Expenses RLS with role-based approval
CREATE POLICY expenses_approve ON expenses
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid() 
      AND role = 'accountant'
    )
  )
  WITH CHECK (
    company_id IN (
      SELECT company_id FROM company_users 
      WHERE user_id = auth.uid() 
      AND role = 'accountant'
    )
  );
```

### **API Security Middleware**

```typescript
// Rate limiting configuration
const rateLimits = {
  '/api/auth/*': { window: '15m', max: 5 },
  '/api/documents': { window: '1m', max: 30 },
  '/api/ita/*': { window: '1m', max: 10 },
  '/api/reports/*': { window: '1m', max: 10 }
};

// Input validation schemas
const validationSchemas = {
  businessNumber: /^[0-9]{9}$/,
  vatId: /^[0-9]{9}$/,
  israeliPhone: /^(\+972|0)(|5[0-9]|7[1-9])[0-9]{7}$/,
  amount: /^\d+(\.\d{1,2})?$/
};

// Audit logging middleware
function auditLog(action: string, entity: string) {
  return async (req, res, next) => {
    const userId = req.user?.id;
    const companyId = req.user?.company_id;
    const entityId = req.params.id || req.body.id;
    
    await supabase.from('audit_log').insert({
      company_id: companyId,
      user_id: userId,
      action,
      entity_type: entity,
      entity_id: entityId,
      old_values: req.method === 'PATCH' ? req.existingData : null,
      new_values: req.body,
      ip_address: req.ip,
      user_agent: req.headers['user-agent']
    });
    
    next();
  };
}
```

## **Part 6: Mobile App Specifications**

### **iOS App Architecture**

```swift
// Technology: SwiftUI + Combine
// Minimum iOS: 15.0
// Architecture: MVVM

// Core Dependencies
dependencies:
  - Supabase Swift SDK
  - PDFKit (for document viewing)
  - Vision (for OCR)
  - MessageUI (for email)

// App Structure
InvoiceApp/
  ├── Models/
  │   ├── Document.swift
  │   ├── Customer.swift
  │   ├── Expense.swift
  │   └── Company.swift
  ├── ViewModels/
  │   ├── AuthViewModel.swift
  │   ├── DocumentViewModel.swift
  │   └── ExpenseViewModel.swift
  ├── Views/
  │   ├── Auth/
  │   ├── Dashboard/
  │   ├── Documents/
  │   └── Expenses/
  ├── Services/
  │   ├── SupabaseService.swift
  │   ├── ITAService.swift
  │   └── PDFService.swift
  └── Utils/
      ├── Extensions.swift
      └── Constants.swift
```

### **Key Mobile Views**

```swift
// Dashboard View
struct DashboardView: View {
    @StateObject var viewModel = DashboardViewModel()
    
    var body: some View {
        NavigationView {
            ScrollView {
                // Stats Cards
                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                    StatCard(title: "החודש", 
                            value: viewModel.monthlyRevenue,
                            trend: viewModel.revenueTrend)
                    StatCard(title: "מע״מ לתשלום",
                            value: viewModel.vatLiability,
                            subtitle: viewModel.nextPaymentDate)
                }
                
                // Quick Actions
                HStack(spacing: 16) {
                    QuickActionButton(icon: "doc.text.fill",
                                    title: "חשבונית",
                                    action: viewModel.createInvoice)
                    QuickActionButton(icon: "camera.fill",
                                    title: "סרוק הוצאה",
                                    action: viewModel.scanExpense)
                }
                
                // Recent Documents
                RecentDocumentsList(documents: viewModel.recentDocuments)
            }
            .navigationTitle("לוח בקרה")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NotificationBadge(count: viewModel.pendingExpenses)
                }
            }
        }
    }
}

// Document Creation Flow
struct CreateDocumentView: View {
    @StateObject var viewModel = CreateDocumentViewModel()
    @State private var currentStep = 0
    
    var body: some View {
        VStack {
            // Progress Indicator
            ProgressView(value: Double(currentStep), total: 3)
                .padding()
            
            // Step Content
            TabView(selection: $currentStep) {
                CustomerSelectionStep()
                    .tag(0)
                ItemsEntryStep()
                    .tag(1)
                ReviewAndSendStep()
                    .tag(2)
            }
            .tabViewStyle(.page(indexDisplayMode: .never))
            
            // Navigation Buttons
            HStack {
                Button("הקודם") {
                    currentStep -= 1
                }
                .disabled(currentStep == 0)
                
                Spacer()
                
                Button(currentStep == 2 ? "שלח" : "הבא") {
                    if currentStep == 2 {
                        viewModel.submitDocument()
                    } else {
                        currentStep += 1
                    }
                }
            }
            .padding()
        }
    }
}
```

## **Part 7: Supabase Edge Functions**

### **Document Processing Function**

```typescript
// supabase/functions/process-document/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const { document_id } = await req.json()
  
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL'),
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
  )
  
  // Fetch document
  const { data: document } = await supabase
    .from('documents')
    .select(`
      *,
      company:companies(*),
      customer:customers(*),
      items:document_items(*)
    `)
    .eq('id', document_id)
    .single()
  
  // Submit to ITA
  const itaPayload = buildITAPayload(document)
  const itaResponse = await submitToITA(itaPayload)
  
  if (itaResponse.success) {
    // Update document with allocation number
    await supabase
      .from('documents')
      .update({
        status: 'approved',
        ita_allocation_number: itaResponse.allocation_number,
        ita_allocation_date: new Date().toISOString()
      })
      .eq('id', document_id)
    
    // Generate PDF
    const pdfUrl = await generatePDF(document)
    
    // Update with PDF URL
    await supabase
      .from('documents')
      .update({ pdf_url: pdfUrl })
      .eq('id', document_id)
    
    return new Response(JSON.stringify({ 
      success: true, 
      allocation_number: itaResponse.allocation_number,
      pdf_url: pdfUrl
    }))
  } else {
    // Queue for retry
    await supabase
      .from('ita_queue')
      .insert({
        document_id,
        status: 'pending',
        request_payload: itaPayload,
        error_message: itaResponse.error,
        next_retry_at: calculateNextRetry(1)
      })
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: itaResponse.error,
      queued: true
    }))
  }
})
```

### **Email Scanner Function**

```typescript
// supabase/functions/scan-emails/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { OAuth2Client } from "https://deno.land/x/oauth2_client/mod.ts"

serve(async (req) => {
  const { email_account_id } = await req.json()
  
  // Fetch email account
  const { data: account } = await supabase
    .from('email_accounts')
    .select('*')
    .eq('id', email_account_id)
    .single()
  
  // Initialize OAuth client
  const oauth = new OAuth2Client({
    clientId: Deno.env.get(`${account.provider.toUpperCase()}_CLIENT_ID`),
    clientSecret: Deno.env.get(`${account.provider.toUpperCase()}_CLIENT_SECRET`),
    tokenEndpoint: getTokenEndpoint(account.provider)
  })
  
  // Refresh token if needed
  if (new Date(account.token_expires_at) < new Date()) {
    const newTokens = await oauth.refreshToken(account.refresh_token)
    await updateTokens(account.id, newTokens)
  }
  
  // Fetch emails
  const emails = await fetchEmails(account)
  
  let newExpenses = 0
  let duplicates = 0
  
  for (const email of emails) {
    for (const attachment of email.attachments) {
      // Download and process
      const fileData = await downloadAttachment(attachment)
      const extracted = await processWithAI(fileData)
      
      if (extracted.is_invoice) {
        // Check for duplicates
        const duplicateCheck = await detectDuplicate(extracted)
        
        // Create expense
        await supabase
          .from('expenses')
          .insert({
            company_id: account.company_id,
            vendor_name: extracted.vendor_name,
            expense_date: extracted.date,
            amount: extracted.amount_before_vat,
            vat_amount: extracted.vat_amount,
            total_amount: extracted.total_amount,
            currency: extracted.currency || 'ILS',
            category: categorizeExpense(extracted.vendor_name),
            status: 'pending',
            duplicate_risk: duplicateCheck.risk_level,
            duplicate_of_id: duplicateCheck.potential_duplicate_id,
            source: 'email_scan',
            source_email_id: account.id,
            original_file_url: fileData.url,
            extracted_data: extracted
          })
        
        if (duplicateCheck.risk_level !== 'none') {
          duplicates++
        } else {
          newExpenses++
        }
      }
    }
  }
  
  // Update last sync
  await supabase
    .from('email_accounts')
    .update({ last_sync_at: new Date().toISOString() })
    .eq('id', email_account_id)
  
  return new Response(JSON.stringify({
    scanned_count: emails.length,
    new_expenses: newExpenses,
    duplicates: duplicates
  }))
})
```

## **Part 8: Database Migrations**

```sql
-- Migration: 001_initial_schema.sql
-- Create all tables with proper order for foreign keys

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enums
CREATE TYPE subscription_tier AS ENUM ('free', 'paid');
CREATE TYPE user_role AS ENUM ('admin', 'user', 'accountant');
CREATE TYPE document_type AS ENUM ('tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt');
CREATE TYPE document_status AS ENUM ('draft', 'pending_allocation', 'approved', 'sent', 'paid', 'cancelled');
CREATE TYPE expense_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE expense_category AS ENUM ('office_supplies', 'travel', 'utilities', 'rent', 'professional_services', 'marketing', 'equipment', 'other');
CREATE TYPE duplicate_risk AS ENUM ('none', 'low', 'high');
CREATE TYPE email_provider AS ENUM ('gmail', 'outlook');

-- Create companies table
CREATE TABLE companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  business_number VARCHAR(9) UNIQUE NOT NULL,
  name_hebrew VARCHAR(255) NOT NULL,
  name_english VARCHAR(255),
  vat_id VARCHAR(9) NOT NULL,
  address_hebrew TEXT NOT NULL,
  address_english TEXT,
  city_hebrew VARCHAR(100) NOT NULL,
  city_english VARCHAR(100),
  postal_code VARCHAR(7),
  phone VARCHAR(15) NOT NULL,
  email VARCHAR(255) NOT NULL,
  logo_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_expires_at TIMESTAMP,
  industry VARCHAR(100) NOT NULL,
  annual_revenue VARCHAR(50),
  interested_in_loan BOOLEAN DEFAULT false,
  interested_in_insurance BOOLEAN DEFAULT false,
  interested_in_accounting BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_companies_business_number ON companies(business_number);
CREATE INDEX idx_companies_subscription ON companies(subscription_tier, subscription_expires_at);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Continue with all other tables...
-- [Include all tables from Part 1 with proper CREATE statements]

-- Enable Row Level Security
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Create all RLS policies
-- [Include all policies from Part 5]
```

## **Part 9: Testing Scenarios**

### **Critical Test Cases**

```typescript
// Test Suite: Document Creation and ITA Integration
describe('Document Creation Flow', () => {
  test('Sequential numbering prevents gaps', async () => {
    // Create 2 concurrent requests
    const promises = [
      createDocument({ type: 'tax_invoice' }),
      createDocument({ type: 'tax_invoice' })
    ]
    const results = await Promise.all(promises)
    
    // Verify different sequential numbers
    expect(results.document_number).not.toBe(results.document_number)
    
    // Verify no gaps
    const numbers = results.map(r => parseInt(r.document_number.split('-').pop()))
    expect(Math.abs(numbers - numbers)).toBe(1)
  })
  
  test('ITA submission retry on failure', async () => {
    // Mock ITA API to fail first time
    mockITAAPI.failNext()
    
    const doc = await createDocument({ type: 'tax_invoice' })
    expect(doc.status).toBe('pending_allocation')
    
    // Check retry queue
    const queue = await getRetryQueue(doc.id)
    expect(queue.status).toBe('pending')
    expect(queue.next_retry_at).toBeDefined()
    
    // Process retry
    await processRetryQueue()
    
    // Verify success
    const updated = await getDocument(doc.id)
    expect(updated.status).toBe('approved')
    expect(updated.ita_allocation_number).toBeDefined()
  })
  
  test('VAT calculation accuracy', () => {
    const items = [
      { quantity: 2.5, unit_price: 100, vat_rate: 18 },
      { quantity: 1, unit_price: 50.50, vat_rate: 18 }
    ]
    
    const result = calculateDocumentTotals({ items })
    
    expect(result.subtotal).toBe(300.50)
    expect(result.vat_amount).toBe(54.09) // 18% of 300.50
    expect(result.total_amount).toBe(354.59)
  })
})

// Test Suite: Expense Scanning
describe('Email Expense Scanning', () => {
  test('Duplicate detection accuracy', async () => {
    // Create existing expense
    await createExpense({
      vendor_name: 'Office Depot Ltd',
      amount: 1000,
      expense_date: '2025-08-29'
    })
    
    // Test exact duplicate
    const exact = await detectDuplicate({
      vendor_name: 'Office Depot Ltd',
      amount: 1000,
      expense_date: '2025-08-29'
    })
    expect(exact.risk_level).toBe('high')
    
    // Test similar
    const similar = await detectDuplicate({
      vendor_name: 'Office Depot',  // Slightly different name
      amount: 1000,
      expense_date: '2025-08-30'  // Different date
    })
    expect(similar.risk_level).toBe('low')
    
    // Test non-duplicate
    const different = await detectDuplicate({
      vendor_name: 'Staples',
      amount: 500,
      expense_date: '2025-08-29'
    })
    expect(different.risk_level).toBe('none')
  })
})
```

## **Part 10: Deployment Configuration**

### **Supabase Project Setup**

```yaml
# supabase/config.toml
[project]
id = "your-project-id"
name = "israeli-invoice-app"

[api]
enabled = true
schemas = ["public"]
extra_search_path = ["public", "extensions"]

[auth]
site_url = "https://your-app.com"
redirect_urls = ["https://your-app.com/auth/callback"]
jwt_expiry = 3600
enable_signup = true

[auth.email]
enable_signup = true
enable_email_confirm = true
template.confirmation = "Confirm your email for Israeli Invoice App"

[storage]
file_size_limit = "10MB"

[storage.buckets.documents]
public = false
file_size_limit = "5MB"
allowed_mime_types = ["application/pdf"]

[storage.buckets.expenses]
public = false
file_size_limit = "10MB"
allowed_mime_types = ["application/pdf", "image/jpeg", "image/png"]

[functions]
"process-document" = { schedule = "", verify_jwt = true }
"scan-emails" = { schedule = "*/30 * * * *", verify_jwt = false }
"process-ita-queue" = { schedule = "*/5 * * * *", verify_jwt = false }
```

### **Environment Variables**

```bash
# .env.production
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=xxx
SUPABASE_SERVICE_ROLE_KEY=xxx

# ITA Integration
ITA_API_URL=https://api.taxes.gov.il/shaam/v1
ITA_API_KEY=xxx
ITA_CERTIFICATE_PATH=/secrets/certificate.p12
ITA_CERTIFICATE_PASSWORD=xxx

# Email Providers
GMAIL_CLIENT_ID=xxx
GMAIL_CLIENT_SECRET=xxx
OUTLOOK_CLIENT_ID=xxx
OUTLOOK_CLIENT_SECRET=xxx

# AI Services
OPENAI_API_KEY=xxx

# URL Shortener
BITLY_ACCESS_TOKEN=xxx
```

## **Part 11: Implementation Checklist**

### **Phase 1: MVP (Week 1)**
- [ ] Supabase project setup
- [ ] Database schema creation
- [ ] Authentication flow
- [ ] Company registration
- [ ] Basic invoice creation
- [ ] ITA integration (direct API)
- [ ] PDF generation
- [ ] Customer management
- [ ] Product catalog

### **Phase 2: Core Features (Week 2)**
- [ ] Email delivery
- [ ] WhatsApp integration
- [ ] Credit notes
- [ ] Receipt creation
- [ ] VAT report generation
- [ ] Expense manual entry
- [ ] AI email scanner setup
- [ ] Duplicate detection
- [ ] Mobile app MVP

### **Phase 3: Polish & Testing (Month 1)**
- [ ] ITA retry mechanism
- [ ] Error handling
- [ ] Performance optimization
- [ ] Security audit
- [ ] Beta user onboarding
- [ ] Bug fixes
- [ ] Documentation

This exhaustive specification provides everything needed for implementation. Each component is detailed enough that developers can code directly from these specifications without ambiguity. The system is designed to be fully compliant with Israeli tax law while maintaining simplicity and scalability.
