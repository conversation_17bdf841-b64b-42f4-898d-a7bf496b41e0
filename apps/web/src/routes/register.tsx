import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Progress } from '@fintech/ui'

interface AccountFormData {
  email: string
  password: string
  fullName: string
  phone: string
}

interface BusinessFormData {
  companyId: string
  nameHebrew: string
  nameEnglish: string
  addressHebrew: string
  cityHebrew: string
  phone: string
}

interface SurveyFormData {
  industry: string
  annualRevenue: string
  interestedInLoan: boolean
  interestedInInsurance: boolean
  interestedInAccounting: boolean
}

function RegisterPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  
  const [accountData, setAccountData] = useState<AccountFormData>({
    email: '',
    password: '',
    fullName: '',
    phone: ''
  })
  
  const [businessData, setBusinessData] = useState<BusinessFormData>({
    companyId: '',
    nameHebrew: '',
    nameEnglish: '',
    addressHebrew: '',
    cityHebrew: '',
    phone: ''
  })
  
  const [surveyData, setSurveyData] = useState<SurveyFormData>({
    industry: '',
    annualRevenue: '',
    interestedInLoan: true,
    interestedInInsurance: true,
    interestedInAccounting: true
  })

  const steps = ['תחום עיסוק', 'פרטי עסק', 'פרטי חשבון', 'שירותי הנהלת חשבונות', 'ביטוח עסקי', 'הלוואות עסקיות']
  const progress = (currentStep / steps.length) * 100

  const handleNext = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      // Prepare registration data with survey responses
      const registrationData = {
        email: accountData.email,
        password: accountData.password,
        full_name: accountData.fullName,
        phone: accountData.phone,
        company: {
          business_number: businessData.companyId,
          name_hebrew: businessData.nameHebrew,
          name_english: businessData.nameEnglish,
          address_hebrew: businessData.addressHebrew,
          city_hebrew: businessData.cityHebrew,
          phone: businessData.phone,
          industry: surveyData.industry,
          annual_revenue: surveyData.annualRevenue,
          interested_in_loan: surveyData.interestedInLoan,
          interested_in_insurance: surveyData.interestedInInsurance,
          interested_in_accounting: surveyData.interestedInAccounting,
        }
      }

      console.log('Registration data:', registrationData)
      // TODO: Send to Supabase registration endpoint
      // const response = await fetch('/api/auth/register', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(registrationData)
      // })
    } catch (error) {
      console.error('Registration error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">כתובת אימייל</Label>
        <Input
          id="email"
          type="email"
          value={accountData.email}
          onChange={(e) => setAccountData({...accountData, email: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="<EMAIL>"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="password">סיסמה</Label>
        <Input
          id="password"
          type="password"
          value={accountData.password}
          onChange={(e) => setAccountData({...accountData, password: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="••••••••"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="fullName">שם מלא</Label>
        <Input
          id="fullName"
          value={accountData.fullName}
          onChange={(e) => setAccountData({...accountData, fullName: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="שם פרטי ומשפחה"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="phone">טלפון</Label>
        <Input
          id="phone"
          type="tel"
          value={accountData.phone}
          onChange={(e) => setAccountData({...accountData, phone: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="050-1234567"
          required
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="companyId">ע.מ / ח.פ</Label>
        <Input
          id="companyId"
          value={businessData.companyId}
          onChange={(e) => setBusinessData({...businessData, companyId: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="*********"
          pattern="[0-9]{9}"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nameHebrew">שם העסק (עברית)</Label>
        <Input
          id="nameHebrew"
          value={businessData.nameHebrew}
          onChange={(e) => setBusinessData({...businessData, nameHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="שם החברה בעברית"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nameEnglish">שם העסק (אנגלית)</Label>
        <Input
          id="nameEnglish"
          value={businessData.nameEnglish}
          onChange={(e) => setBusinessData({...businessData, nameEnglish: e.target.value})}
          className="text-left"
          dir="ltr"
          placeholder="Company Name in English"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="addressHebrew">כתובת</Label>
        <Input
          id="addressHebrew"
          value={businessData.addressHebrew}
          onChange={(e) => setBusinessData({...businessData, addressHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="רחוב, מספר בית, עיר"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="cityHebrew">עיר</Label>
        <Input
          id="cityHebrew"
          value={businessData.cityHebrew}
          onChange={(e) => setBusinessData({...businessData, cityHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="תל אביב"
          required
        />
      </div>
    </div>
  )

  const renderSurveyStep1 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="industry">תחום עיסוק</Label>
        <select
          id="industry"
          value={surveyData.industry}
          onChange={(e) => setSurveyData({...surveyData, industry: e.target.value})}
          className="w-full p-2 border border-input bg-background rounded-md text-right"
          dir="rtl"
          required
        >
          <option value="">בחר תחום</option>
          <option value="technology">טכנולוגיה</option>
          <option value="retail">קמעונאות</option>
          <option value="services">שירותים</option>
          <option value="manufacturing">ייצור</option>
          <option value="construction">בנייה</option>
          <option value="healthcare">בריאות</option>
          <option value="other">אחר</option>
        </select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="annualRevenue">מחזור שנתי</Label>
        <select
          id="annualRevenue"
          value={surveyData.annualRevenue}
          onChange={(e) => setSurveyData({...surveyData, annualRevenue: e.target.value})}
          className="w-full p-2 border border-input bg-background rounded-md text-right"
          dir="rtl"
          required
        >
          <option value="">בחר טווח</option>
          <option value="0-50K">0-50 אלף ₪</option>
          <option value="50K-100K">50-100 אלף ₪</option>
          <option value="100K-250K">100-250 אלף ₪</option>
          <option value="250K-500K">250-500 אלף ₪</option>
          <option value="500K-1M">500 אלף - 1 מיליון ₪</option>
          <option value="1M-2.5M">1-2.5 מיליון ₪</option>
          <option value="2.5M-5M">2.5-5 מיליון ₪</option>
          <option value="5M+">מעל 5 מיליון ₪</option>
        </select>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6 text-center">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">שירותי הנהלת חשבונות</h3>
        <p className="text-muted-foreground">האם תהיה מעוניין לשמוע הצעה ייחודית לשירותי הנהלת חשבונות?</p>
      </div>

      <div className="flex justify-center space-x-4 space-x-reverse">
        <Button
          variant={surveyData.interestedInAccounting ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInAccounting: true})}
          className="w-24"
        >
          כן
        </Button>
        <Button
          variant={!surveyData.interestedInAccounting ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInAccounting: false})}
          className="w-24"
        >
          לא
        </Button>
      </div>
    </div>
  )

  const renderStep4 = () => (
    <div className="space-y-6 text-center">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">ביטוח עסקי</h3>
        <p className="text-muted-foreground">האם תהיה מעוניין לשמוע לגבי הצעה מיוחדת לביטוח העסק?</p>
      </div>

      <div className="flex justify-center space-x-4 space-x-reverse">
        <Button
          variant={surveyData.interestedInInsurance ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInInsurance: true})}
          className="w-24"
        >
          כן
        </Button>
        <Button
          variant={!surveyData.interestedInInsurance ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInInsurance: false})}
          className="w-24"
        >
          לא
        </Button>
      </div>
    </div>
  )

  const renderStep5 = () => (
    <div className="space-y-6 text-center">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">הלוואות עסקיות</h3>
        <p className="text-muted-foreground">האם תהיה מעוניין לשמוע הצעה ייחודית להלוואות עסקיות?</p>
      </div>

      <div className="flex justify-center space-x-4 space-x-reverse">
        <Button
          variant={surveyData.interestedInLoan ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInLoan: true})}
          className="w-24"
        >
          כן
        </Button>
        <Button
          variant={!surveyData.interestedInLoan ? "default" : "outline"}
          onClick={() => setSurveyData({...surveyData, interestedInLoan: false})}
          className="w-24"
        >
          לא
        </Button>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen flex items-center justify-center bg-background cosmic-grid px-4">
      <Card className="w-full max-w-2xl cosmic-glass">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold font-secular">הרשמה</CardTitle>
          <CardDescription>
            צור חשבון חדש למערכת החשבוניות
          </CardDescription>
          
          {/* Progress Indicator */}
          <div className="mt-4">
            <Progress value={progress} className="w-full" />
          </div>
        </CardHeader>
        
        <CardContent>
          {currentStep === 1 && renderSurveyStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep1()}
          {currentStep === 4 && renderStep3()}
          {currentStep === 5 && renderStep4()}
          {currentStep === 6 && renderStep5()}

          <div className="flex justify-between mt-6">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 1}
            >
              הקודם
            </Button>

            {currentStep < 6 ? (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && (!surveyData.industry || !surveyData.annualRevenue)) ||
                  (currentStep === 2 && (!businessData.companyId || !businessData.nameHebrew || !businessData.addressHebrew || !businessData.cityHebrew)) ||
                  (currentStep === 3 && (!accountData.email || !accountData.password || !accountData.fullName || !accountData.phone))
                }
              >
                הבא
              </Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? 'נרשם...' : 'הירשם'}
              </Button>
            )}
          </div>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              יש לך כבר חשבון?{' '}
              <Link 
                to="/login" 
                className="text-primary hover:underline font-medium"
              >
                התחבר כאן
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/register')({
  component: RegisterPage,
})
