// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789 /* InvoiceAppApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456788 /* InvoiceAppApp.swift */; };
		A1234567890123456791 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456790 /* ContentView.swift */; };
		A1234567890123456793 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456792 /* Assets.xcassets */; };
		A1234567890123456796 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456795 /* Preview Assets.xcassets */; };
		A1234567890123456798 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = A1234567890123456797 /* Supabase */; };
		A1234567890123456800 /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = A1234567890123456799 /* Auth */; };
		A1234567890123456802 /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = A1234567890123456801 /* PostgREST */; };
		A1234567890123456804 /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = A1234567890123456803 /* Realtime */; };
		A1234567890123456806 /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = A1234567890123456805 /* Storage */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1234567890123456785 /* InvoiceApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = InvoiceApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456788 /* InvoiceAppApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvoiceAppApp.swift; sourceTree = "<group>"; };
		A1234567890123456790 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890123456792 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456795 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890123456807 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890123456782 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456798 /* Supabase in Frameworks */,
				A1234567890123456800 /* Auth in Frameworks */,
				A1234567890123456802 /* PostgREST in Frameworks */,
				A1234567890123456804 /* Realtime in Frameworks */,
				A1234567890123456806 /* Storage in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890123456779 = {
			isa = PBXGroup;
			children = (
				A1234567890123456784 /* InvoiceApp */,
				A1234567890123456786 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1234567890123456784 /* InvoiceApp */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456788 /* InvoiceAppApp.swift */,
				A1234567890123456790 /* ContentView.swift */,
				A1234567890123456808 /* Models */,
				A1234567890123456809 /* ViewModels */,
				A1234567890123456810 /* Views */,
				A1234567890123456811 /* Services */,
				A1234567890123456812 /* Utils */,
				A1234567890123456792 /* Assets.xcassets */,
				A1234567890123456807 /* Info.plist */,
				A1234567890123456794 /* Preview Content */,
			);
			path = InvoiceApp;
			sourceTree = "<group>";
		};
		A1234567890123456786 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456785 /* InvoiceApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890123456794 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456795 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A1234567890123456808 /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1234567890123456809 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A1234567890123456810 /* Views */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456813 /* Auth */,
				A1234567890123456814 /* Dashboard */,
				A1234567890123456815 /* Documents */,
				A1234567890123456816 /* Expenses */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1234567890123456811 /* Services */ = {
			isa = PBXGroup;
			children = (
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1234567890123456812 /* Utils */ = {
			isa = PBXGroup;
			children = (
			);
			path = Utils;
			sourceTree = "<group>";
		};
		A1234567890123456813 /* Auth */ = {
			isa = PBXGroup;
			children = (
			);
			path = Auth;
			sourceTree = "<group>";
		};
		A1234567890123456814 /* Dashboard */ = {
			isa = PBXGroup;
			children = (
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		A1234567890123456815 /* Documents */ = {
			isa = PBXGroup;
			children = (
			);
			path = Documents;
			sourceTree = "<group>";
		};
		A1234567890123456816 /* Expenses */ = {
			isa = PBXGroup;
			children = (
			);
			path = Expenses;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456784 /* InvoiceApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456799 /* Build configuration list for PBXNativeTarget "InvoiceApp" */;
			buildPhases = (
				A1234567890123456781 /* Sources */,
				A1234567890123456782 /* Frameworks */,
				A1234567890123456783 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = InvoiceApp;
			packageProductDependencies = (
				A1234567890123456797 /* Supabase */,
				A1234567890123456799 /* Auth */,
				A1234567890123456801 /* PostgREST */,
				A1234567890123456803 /* Realtime */,
				A1234567890123456805 /* Storage */,
			);
			productName = InvoiceApp;
			productReference = A1234567890123456785 /* InvoiceApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456780 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890123456784 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1234567890123456783 /* Build configuration list for PBXProject "InvoiceApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = he;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				he,
			);
			mainGroup = A1234567890123456779;
			packageReferences = (
				A1234567890123456796 /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			productRefGroup = A1234567890123456786 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456784 /* InvoiceApp */,
			);
		};
/* End PBXProject section */

/* Begin XCBuildConfiguration section */
		A1234567890123456797 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

		rootObject = A1234567890123456780 /* Project object */;
}
