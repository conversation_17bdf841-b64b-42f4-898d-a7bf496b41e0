// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "InvoiceApp",
    platforms: [
        .iOS(.v15)
    ],
    products: [
        .library(
            name: "InvoiceApp",
            targets: ["InvoiceApp"]
        ),
    ],
    dependencies: [
        // Supabase Swift SDK
        .package(
            url: "https://github.com/supabase/supabase-swift.git",
            from: "2.5.1"
        ),
        
        // Additional dependencies for future features
        .package(
            url: "https://github.com/apple/swift-collections.git",
            from: "1.0.0"
        ),
        
        // For PDF generation and manipulation
        .package(
            url: "https://github.com/WeTransfer/WeScan.git",
            from: "1.8.0"
        ),
        
        // For image processing and OCR
        .package(
            url: "https://github.com/apple/swift-algorithms.git",
            from: "1.0.0"
        ),
    ],
    targets: [
        .target(
            name: "InvoiceApp",
            dependencies: [
                .product(name: "Supabase", package: "supabase-swift"),
                .product(name: "Auth", package: "supabase-swift"),
                .product(name: "PostgREST", package: "supabase-swift"),
                .product(name: "Realtime", package: "supabase-swift"),
                .product(name: "Storage", package: "supabase-swift"),
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "WeScan", package: "WeScan"),
                .product(name: "Algorithms", package: "swift-algorithms"),
            ],
            path: "InvoiceApp",
            resources: [
                .process("Assets.xcassets"),
                .process("Preview Content"),
            ]
        ),
        .testTarget(
            name: "InvoiceAppTests",
            dependencies: ["InvoiceApp"]
        ),
    ]
)
