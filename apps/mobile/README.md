# Invoice App - iOS Mobile Application

A comprehensive iOS mobile application for invoice and expense management, built with SwiftUI and integrated with Supabase backend.

## Features

### Core Functionality
- **Multi-step Registration**: Company setup with survey-based onboarding
- **Authentication**: Secure login/logout with <PERSON>pa<PERSON> Auth
- **Dashboard**: Real-time business metrics and quick actions
- **Document Management**: Create, view, and manage invoices and receipts
- **Expense Tracking**: Scan receipts, categorize expenses, approve/reject workflow
- **Reports**: VAT reports, income/expense analysis, customer insights
- **Settings**: Company profile, subscription management, app preferences

### Design System
- **RTL Support**: Full Hebrew language support with right-to-left layout
- **Dark/Light Mode**: Automatic theme switching with user preference
- **Cosmic Design**: Minimalist black and white aesthetic with subtle glow effects
- **Typography**: Custom Hebrew fonts (SecularOne) and Latin fonts (Inter)
- **Responsive**: Optimized for iPhone and iPad

## Architecture

### MVVM Pattern
- **Models**: Data structures for User, Company, Document, Expense
- **ViewModels**: Business logic and state management with Combine
- **Views**: SwiftUI views with reusable components
- **Services**: Supabase integration and API communication

### Project Structure
```
InvoiceApp/
├── InvoiceAppApp.swift          # App entry point
├── ContentView.swift            # Main navigation
├── Models/                      # Data models
├── ViewModels/                  # MVVM view models
├── Views/                       # SwiftUI views
│   ├── Auth/                   # Authentication flows
│   ├── Dashboard/              # Main dashboard
│   ├── Documents/              # Document management
│   ├── Expenses/               # Expense tracking
│   ├── Reports/                # Business reports
│   └── Settings/               # App settings
├── Services/                    # Backend services
├── Utils/                       # Utilities and helpers
└── Assets.xcassets             # App assets
```

## Dependencies

### Core Dependencies
- **Supabase Swift SDK**: Backend integration
  - Auth: User authentication
  - PostgREST: Database operations
  - Realtime: Live updates
  - Storage: File management

### Additional Dependencies
- **Swift Collections**: Enhanced data structures
- **WeScan**: Document scanning capabilities
- **Swift Algorithms**: Advanced algorithms

## Setup Instructions

### Prerequisites
- Xcode 15.0 or later
- iOS 15.0 or later
- Swift 5.9 or later
- Active Supabase project

### Environment Configuration
1. Create a `.env` file in the project root:
```bash
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

2. Configure Supabase credentials in the app:
   - Update `SupabaseService.swift` with your project details
   - Ensure database schema matches the expected structure

### Build and Run
1. Open `InvoiceApp.xcodeproj` in Xcode
2. Select your target device or simulator
3. Build and run the project (⌘+R)

## App Store Compliance

### Privacy and Security
- **Privacy Manifest**: Compliant with iOS privacy requirements
- **Data Encryption**: All network communication uses TLS 1.2+
- **Permissions**: Camera and photo library access for receipt scanning
- **No Tracking**: App does not track users across other apps

### Localization
- **Primary Language**: Hebrew (he)
- **Secondary Language**: English (en)
- **RTL Layout**: Proper right-to-left text and UI layout

### App Store Guidelines
- **Content Rating**: 4+ (suitable for all ages)
- **Categories**: Business, Finance
- **Keywords**: חשבוניות, הוצאות, עסק, מע״מ, קבלות

## Testing

### Unit Tests
- Model validation and business logic
- ViewModel state management
- Service layer functionality

### Integration Tests
- Supabase API integration
- Authentication flows
- Data persistence

### UI Tests
- Navigation flows
- Form validation
- Accessibility compliance

## Deployment

### Development
1. Configure development team in Xcode
2. Set up provisioning profiles
3. Test on physical devices

### App Store Release
1. Archive the app in Xcode
2. Upload to App Store Connect
3. Complete app metadata and screenshots
4. Submit for review

## Security Considerations

### Data Protection
- Sensitive data encrypted at rest
- Secure keychain storage for tokens
- Network requests use certificate pinning

### Authentication
- JWT token-based authentication
- Automatic token refresh
- Secure logout with token invalidation

### Privacy
- Minimal data collection
- User consent for camera access
- No third-party analytics or tracking

## Performance Optimization

### Memory Management
- Lazy loading for large datasets
- Image caching and compression
- Proper view lifecycle management

### Network Efficiency
- Request batching and caching
- Offline capability for core features
- Background sync for data updates

### UI Performance
- SwiftUI best practices
- Efficient list rendering
- Smooth animations and transitions

## Accessibility

### VoiceOver Support
- Proper accessibility labels
- Semantic markup for screen readers
- Keyboard navigation support

### Dynamic Type
- Scalable fonts for better readability
- Layout adaptation for larger text sizes
- High contrast mode support

## Future Enhancements

### Planned Features
- AI-powered expense categorization
- WhatsApp integration for invoice sharing
- Advanced reporting with charts
- Multi-company support
- Offline mode with sync

### Technical Improvements
- Core Data integration for offline storage
- Push notifications for important updates
- Widget support for quick actions
- Siri shortcuts integration

## Support and Documentation

### Help Resources
- In-app help center
- Video tutorials
- FAQ section
- Contact support

### Developer Resources
- API documentation
- Code examples
- Best practices guide
- Troubleshooting guide

## License

This project is proprietary software. All rights reserved.

## Contact

For technical support or questions:
- Email: <EMAIL>
- Website: https://invoiceapp.com
- Documentation: https://docs.invoiceapp.com
