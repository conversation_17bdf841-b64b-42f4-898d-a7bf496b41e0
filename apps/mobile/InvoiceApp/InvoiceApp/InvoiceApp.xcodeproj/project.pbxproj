// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		CE2F1E502E62C91F00A53A00 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CE2F1E3A2E62C91E00A53A00 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CE2F1E412E62C91E00A53A00;
			remoteInfo = InvoiceApp;
		};
		CE2F1E5A2E62C91F00A53A00 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CE2F1E3A2E62C91E00A53A00 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CE2F1E412E62C91E00A53A00;
			remoteInfo = InvoiceApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		CE2F1E422E62C91E00A53A00 /* InvoiceApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = InvoiceApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CE2F1E4F2E62C91F00A53A00 /* InvoiceAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = InvoiceAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		CE2F1E592E62C91F00A53A00 /* InvoiceAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = InvoiceAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		CE2F1E442E62C91E00A53A00 /* InvoiceApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceApp;
			sourceTree = "<group>";
		};
		CE2F1E522E62C91F00A53A00 /* InvoiceAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceAppTests;
			sourceTree = "<group>";
		};
		CE2F1E5C2E62C91F00A53A00 /* InvoiceAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceAppUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		CE2F1E3F2E62C91E00A53A00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E4C2E62C91F00A53A00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E562E62C91F00A53A00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CE2F1E392E62C91E00A53A00 = {
			isa = PBXGroup;
			children = (
				CE2F1E442E62C91E00A53A00 /* InvoiceApp */,
				CE2F1E522E62C91F00A53A00 /* InvoiceAppTests */,
				CE2F1E5C2E62C91F00A53A00 /* InvoiceAppUITests */,
				CE2F1E432E62C91E00A53A00 /* Products */,
			);
			sourceTree = "<group>";
		};
		CE2F1E432E62C91E00A53A00 /* Products */ = {
			isa = PBXGroup;
			children = (
				CE2F1E422E62C91E00A53A00 /* InvoiceApp.app */,
				CE2F1E4F2E62C91F00A53A00 /* InvoiceAppTests.xctest */,
				CE2F1E592E62C91F00A53A00 /* InvoiceAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CE2F1E412E62C91E00A53A00 /* InvoiceApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE2F1E632E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceApp" */;
			buildPhases = (
				CE2F1E3E2E62C91E00A53A00 /* Sources */,
				CE2F1E3F2E62C91E00A53A00 /* Frameworks */,
				CE2F1E402E62C91E00A53A00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				CE2F1E442E62C91E00A53A00 /* InvoiceApp */,
			);
			name = InvoiceApp;
			packageProductDependencies = (
			);
			productName = InvoiceApp;
			productReference = CE2F1E422E62C91E00A53A00 /* InvoiceApp.app */;
			productType = "com.apple.product-type.application";
		};
		CE2F1E4E2E62C91F00A53A00 /* InvoiceAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE2F1E662E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceAppTests" */;
			buildPhases = (
				CE2F1E4B2E62C91F00A53A00 /* Sources */,
				CE2F1E4C2E62C91F00A53A00 /* Frameworks */,
				CE2F1E4D2E62C91F00A53A00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CE2F1E512E62C91F00A53A00 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CE2F1E522E62C91F00A53A00 /* InvoiceAppTests */,
			);
			name = InvoiceAppTests;
			packageProductDependencies = (
			);
			productName = InvoiceAppTests;
			productReference = CE2F1E4F2E62C91F00A53A00 /* InvoiceAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		CE2F1E582E62C91F00A53A00 /* InvoiceAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE2F1E692E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceAppUITests" */;
			buildPhases = (
				CE2F1E552E62C91F00A53A00 /* Sources */,
				CE2F1E562E62C91F00A53A00 /* Frameworks */,
				CE2F1E572E62C91F00A53A00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CE2F1E5B2E62C91F00A53A00 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CE2F1E5C2E62C91F00A53A00 /* InvoiceAppUITests */,
			);
			name = InvoiceAppUITests;
			packageProductDependencies = (
			);
			productName = InvoiceAppUITests;
			productReference = CE2F1E592E62C91F00A53A00 /* InvoiceAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CE2F1E3A2E62C91E00A53A00 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					CE2F1E412E62C91E00A53A00 = {
						CreatedOnToolsVersion = 16.4;
					};
					CE2F1E4E2E62C91F00A53A00 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = CE2F1E412E62C91E00A53A00;
					};
					CE2F1E582E62C91F00A53A00 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = CE2F1E412E62C91E00A53A00;
					};
				};
			};
			buildConfigurationList = CE2F1E3D2E62C91E00A53A00 /* Build configuration list for PBXProject "InvoiceApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CE2F1E392E62C91E00A53A00;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = CE2F1E432E62C91E00A53A00 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CE2F1E412E62C91E00A53A00 /* InvoiceApp */,
				CE2F1E4E2E62C91F00A53A00 /* InvoiceAppTests */,
				CE2F1E582E62C91F00A53A00 /* InvoiceAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CE2F1E402E62C91E00A53A00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E4D2E62C91F00A53A00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E572E62C91F00A53A00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CE2F1E3E2E62C91E00A53A00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E4B2E62C91F00A53A00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE2F1E552E62C91F00A53A00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		CE2F1E512E62C91F00A53A00 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CE2F1E412E62C91E00A53A00 /* InvoiceApp */;
			targetProxy = CE2F1E502E62C91F00A53A00 /* PBXContainerItemProxy */;
		};
		CE2F1E5B2E62C91F00A53A00 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CE2F1E412E62C91E00A53A00 /* InvoiceApp */;
			targetProxy = CE2F1E5A2E62C91F00A53A00 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		CE2F1E612E62C91F00A53A00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CE2F1E622E62C91F00A53A00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CE2F1E642E62C91F00A53A00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CE2F1E652E62C91F00A53A00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		CE2F1E672E62C91F00A53A00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/InvoiceApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/InvoiceApp";
			};
			name = Debug;
		};
		CE2F1E682E62C91F00A53A00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/InvoiceApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/InvoiceApp";
			};
			name = Release;
		};
		CE2F1E6A2E62C91F00A53A00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = InvoiceApp;
			};
			name = Debug;
		};
		CE2F1E6B2E62C91F00A53A00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = InvoiceApp;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CE2F1E3D2E62C91E00A53A00 /* Build configuration list for PBXProject "InvoiceApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE2F1E612E62C91F00A53A00 /* Debug */,
				CE2F1E622E62C91F00A53A00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE2F1E632E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE2F1E642E62C91F00A53A00 /* Debug */,
				CE2F1E652E62C91F00A53A00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE2F1E662E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE2F1E672E62C91F00A53A00 /* Debug */,
				CE2F1E682E62C91F00A53A00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE2F1E692E62C91F00A53A00 /* Build configuration list for PBXNativeTarget "InvoiceAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE2F1E6A2E62C91F00A53A00 /* Debug */,
				CE2F1E6B2E62C91F00A53A00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CE2F1E3A2E62C91E00A53A00 /* Project object */;
}
