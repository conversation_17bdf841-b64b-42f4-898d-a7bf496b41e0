//
//  RegistrationView.swift
//  InvoiceApp
//
//  Multi-step registration view with survey
//

import SwiftUI

struct RegistrationView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            // Progress Bar
            ProgressBarView(
                currentStep: authViewModel.registrationData.currentStep,
                totalSteps: 7
            )
            
            // Content
            TabView(selection: $authViewModel.registrationData.currentStep) {
                CompanyNameStep()
                    .tag(0)
                
                AnnualRevenueStep()
                    .tag(1)
                
                FullNameStep()
                    .tag(2)
                
                EmailStep()
                    .tag(3)
                
                PhoneStep()
                    .tag(4)
                
                CompanyNumberStep()
                    .tag(5)
                
                ServicePreferencesStep()
                    .tag(6)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .animation(.easeInOut, value: authViewModel.registrationData.currentStep)
            
            // Navigation Buttons
            NavigationButtonsView()
        }
        .background(Color.backgroundPrimary)
        .navigationBarHidden(true)
    }
}

struct ProgressBarView: View {
    let currentStep: Int
    let totalSteps: Int
    
    var progress: Double {
        return Double(currentStep) / Double(totalSteps - 1)
    }
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            HStack {
                Text("שלב \(currentStep + 1) מתוך \(totalSteps)")
                    .font(.hebrewCaption)
                    .foregroundColor(.textSecondary)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.hebrewCaption)
                    .foregroundColor(.textSecondary)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .primaryBlue))
                .scaleEffect(x: 1, y: 2, anchor: .center)
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.vertical, Spacing.md)
        .background(Color.backgroundSecondary)
    }
}

struct CompanyNameStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "שם החברה",
            subtitle: "איך קוראים לחברה שלכם?"
        ) {
            VStack(alignment: .trailing, spacing: Spacing.sm) {
                Text("שם החברה")
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                TextField("הכניסו את שם החברה", text: $authViewModel.registrationData.companyName)
                    .inputFieldStyle()
            }
            .hebrewAlignment()
        }
    }
}

struct AnnualRevenueStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "מחזור שנתי",
            subtitle: "מה המחזור השנתי של החברה?"
        ) {
            VStack(spacing: Spacing.md) {
                ForEach(Company.AnnualRevenue.allCases, id: \.self) { revenue in
                    Button {
                        authViewModel.registrationData.annualRevenue = revenue
                    } label: {
                        HStack {
                            Text(revenue.displayName)
                                .font(.hebrewBody)
                                .foregroundColor(.textPrimary)
                            
                            Spacer()
                            
                            Image(systemName: authViewModel.registrationData.annualRevenue == revenue ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(authViewModel.registrationData.annualRevenue == revenue ? .primaryBlue : .neutralGray300)
                        }
                        .padding(.horizontal, Spacing.md)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: CornerRadius.md)
                                .fill(authViewModel.registrationData.annualRevenue == revenue ? Color.primaryBlue.opacity(0.1) : Color.backgroundSecondary)
                                .overlay(
                                    RoundedRectangle(cornerRadius: CornerRadius.md)
                                        .stroke(authViewModel.registrationData.annualRevenue == revenue ? Color.primaryBlue : Color.neutralGray200, lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .hebrewAlignment()
        }
    }
}

struct FullNameStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "שם מלא",
            subtitle: "איך קוראים לכם?"
        ) {
            VStack(alignment: .trailing, spacing: Spacing.sm) {
                Text("שם מלא")
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                TextField("הכניסו את השם המלא שלכם", text: $authViewModel.registrationData.fullName)
                    .inputFieldStyle()
            }
            .hebrewAlignment()
        }
    }
}

struct EmailStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "כתובת אימייל",
            subtitle: "נשתמש בכתובת הזו להתחברות ולעדכונים"
        ) {
            VStack(alignment: .trailing, spacing: Spacing.sm) {
                Text("אימייל")
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                TextField("הכניסו את כתובת האימייל שלכם", text: $authViewModel.registrationData.email)
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .inputFieldStyle()
            }
            .hebrewAlignment()
        }
    }
}

struct PhoneStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "מספר טלפון",
            subtitle: "לצורך יצירת קשר ועדכונים חשובים"
        ) {
            VStack(alignment: .trailing, spacing: Spacing.sm) {
                Text("מספר טלפון")
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                TextField("הכניסו את מספר הטלפון שלכם", text: $authViewModel.registrationData.phone)
                    .keyboardType(.phonePad)
                    .inputFieldStyle()
            }
            .hebrewAlignment()
        }
    }
}

struct CompanyNumberStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "ע.מ / ח.פ",
            subtitle: "מספר החברה או העוסק המורשה"
        ) {
            VStack(alignment: .trailing, spacing: Spacing.sm) {
                Text("ע.מ / ח.פ")
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                TextField("הכניסו את מספר החברה", text: $authViewModel.registrationData.companyNumber)
                    .keyboardType(.numberPad)
                    .inputFieldStyle()
            }
            .hebrewAlignment()
        }
    }
}

struct ServicePreferencesStep: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        RegistrationStepContainer(
            title: "שירותים נוספים",
            subtitle: "באילו שירותים נוספים אתם מעוניינים?"
        ) {
            VStack(spacing: Spacing.md) {
                ServiceToggleRow(
                    title: "שירותי הנהלת חשבונות",
                    subtitle: "ניהול ספרים ודוחות מס",
                    isOn: $authViewModel.registrationData.needsAccounting
                )
                
                ServiceToggleRow(
                    title: "ביטוחים עסקיים",
                    subtitle: "ביטוח אחריות מקצועית ועוד",
                    isOn: $authViewModel.registrationData.needsInsurance
                )
                
                ServiceToggleRow(
                    title: "הלוואות עסקיות",
                    subtitle: "מימון לצמיחה ופיתוח העסק",
                    isOn: $authViewModel.registrationData.needsLoans
                )
            }
        }
    }
}

struct ServiceToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text(title)
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                Text(subtitle)
                    .font(.hebrewCaption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: .primaryBlue))
        }
        .padding(.horizontal, Spacing.md)
        .frame(height: 60)
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.md)
        .hebrewAlignment()
    }
}

struct NavigationButtonsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        HStack(spacing: Spacing.md) {
            if authViewModel.registrationData.currentStep > 0 {
                Button("הקודם") {
                    authViewModel.previousStep()
                }
                .secondaryButtonStyle()
                .frame(maxWidth: .infinity)
            }
            
            Button(authViewModel.registrationData.currentStep == 6 ? "סיום הרשמה" : "הבא") {
                if authViewModel.registrationData.currentStep == 6 {
                    Task {
                        await authViewModel.signUp()
                    }
                } else {
                    authViewModel.nextStep()
                }
            }
            .primaryButtonStyle(isEnabled: authViewModel.canProceedToNextStep && !authViewModel.isRegistering)
            .disabled(!authViewModel.canProceedToNextStep || authViewModel.isRegistering)
            .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.vertical, Spacing.md)
        .background(Color.backgroundSecondary)
    }
}

struct RegistrationStepContainer<Content: View>: View {
    let title: String
    let subtitle: String
    let content: Content
    
    init(title: String, subtitle: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.subtitle = subtitle
        self.content = content()
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: Spacing.xl) {
                VStack(spacing: Spacing.md) {
                    Text(title)
                        .font(.hebrewTitle)
                        .foregroundColor(.textPrimary)
                    
                    Text(subtitle)
                        .font(.hebrewBody)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .hebrewAlignment()
                
                content
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.xl)
        }
    }
}

#Preview {
    RegistrationView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
