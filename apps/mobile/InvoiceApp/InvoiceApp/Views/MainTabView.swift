//
//  MainTabView.swift
//  InvoiceApp
//
//  Main tab navigation for the authenticated app
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            DashboardView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("דף הבית")
                }
                .tag(0)
            
            DocumentsView()
                .tabItem {
                    Image(systemName: "doc.text.fill")
                    Text("מסמכים")
                }
                .tag(1)
            
            ExpensesView()
                .tabItem {
                    Image(systemName: "creditcard.fill")
                    Text("הוצאות")
                }
                .tag(2)
            
            ReportsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("דוחות")
                }
                .tag(3)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("הגדרות")
                }
                .tag(4)
        }
        .accentColor(.primaryBlue)
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: Spacing.lg) {
                    // Header
                    DashboardHeaderView()
                    
                    // Quick Actions
                    QuickActionsView()
                    
                    // Metrics Cards
                    MetricsGridView()
                        .environmentObject(viewModel)
                    
                    // Recent Activity
                    RecentActivityView()
                        .environmentObject(viewModel)
                }
                .padding(.horizontal, Spacing.md)
                .padding(.bottom, Spacing.xl)
            }
            .background(Color.backgroundPrimary)
            .navigationTitle("דף הבית")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await viewModel.refreshData()
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .task {
            await viewModel.loadDashboardData()
        }
    }
}

struct DashboardHeaderView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        HStack {
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text("שלום, \(authViewModel.userDisplayName)")
                    .font(.hebrewHeadline)
                    .foregroundColor(.textPrimary)
                
                Text(authViewModel.userCompanyName)
                    .font(.hebrewBody)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            Button {
                // Profile action
            } label: {
                Circle()
                    .fill(Color.primaryBlue)
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(String(authViewModel.userDisplayName.prefix(1)))
                            .font(.hebrewBodyBold)
                            .foregroundColor(.white)
                    )
            }
        }
        .padding(.horizontal, Spacing.md)
        .hebrewAlignment()
    }
}

struct QuickActionsView: View {
    var body: some View {
        VStack(alignment: .trailing, spacing: Spacing.md) {
            Text("פעולות מהירות")
                .font(.hebrewHeadline)
                .foregroundColor(.textPrimary)
            
            HStack(spacing: Spacing.md) {
                QuickActionButton(
                    icon: "plus.circle.fill",
                    title: "חשבונית חדשה",
                    color: .primaryBlue
                ) {
                    // Create invoice action
                }
                
                QuickActionButton(
                    icon: "camera.fill",
                    title: "סרוק קבלה",
                    color: .accentGreen
                ) {
                    // Scan receipt action
                }
                
                QuickActionButton(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "דוח מע״מ",
                    color: .accentOrange
                ) {
                    // VAT report action
                }
            }
        }
        .hebrewAlignment()
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.sm) {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.hebrewCaption)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(Color.backgroundSecondary)
            .cornerRadius(CornerRadius.md)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct MetricsGridView: View {
    @EnvironmentObject var viewModel: DashboardViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: Spacing.md) {
            Text("סקירה כללית")
                .font(.hebrewHeadline)
                .foregroundColor(.textPrimary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: Spacing.md) {
                MetricCard(
                    title: "הכנסות החודש",
                    value: viewModel.monthlyRevenue,
                    icon: "arrow.up.circle.fill",
                    color: .accentGreen
                )
                
                MetricCard(
                    title: "הוצאות החודש",
                    value: viewModel.monthlyExpenses,
                    icon: "arrow.down.circle.fill",
                    color: .accentRed
                )
                
                MetricCard(
                    title: "רווח נקי",
                    value: viewModel.netProfit,
                    icon: "chart.line.uptrend.xyaxis",
                    color: .primaryBlue
                )
                
                MetricCard(
                    title: "חשבוניות ממתינות",
                    value: viewModel.pendingInvoicesCount,
                    icon: "doc.text.fill",
                    color: .accentOrange
                )
            }
        }
        .hebrewAlignment()
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .trailing, spacing: Spacing.sm) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                
                Spacer()
                
                Text(title)
                    .font(.hebrewCaption)
                    .foregroundColor(.textSecondary)
            }
            
            Text(value)
                .font(.numberMedium)
                .foregroundColor(.textPrimary)
                .frame(maxWidth: .infinity, alignment: .trailing)
        }
        .padding(Spacing.md)
        .cardStyle()
        .hebrewAlignment()
    }
}

struct RecentActivityView: View {
    @EnvironmentObject var viewModel: DashboardViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: Spacing.md) {
            HStack {
                Button("צפה בהכל") {
                    // View all action
                }
                .font(.hebrewCaption)
                .foregroundColor(.primaryBlue)
                
                Spacer()
                
                Text("פעילות אחרונה")
                    .font(.hebrewHeadline)
                    .foregroundColor(.textPrimary)
            }
            
            VStack(spacing: Spacing.sm) {
                ForEach(viewModel.recentDocuments.prefix(3)) { document in
                    RecentActivityRow(
                        title: "חשבונית #\(document.number)",
                        subtitle: document.customerName,
                        amount: formatCurrency(document.total),
                        status: document.status.displayName,
                        statusColor: document.status.color
                    )
                }
                
                ForEach(viewModel.recentExpenses.prefix(2)) { expense in
                    RecentActivityRow(
                        title: expense.description,
                        subtitle: expense.category.displayName,
                        amount: formatCurrency(expense.amount),
                        status: expense.status.displayName,
                        statusColor: expense.status.color
                    )
                }
            }
        }
        .hebrewAlignment()
    }
    
    private func formatCurrency(_ amount: Decimal) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSDecimalNumber(decimal: amount)) ?? "₪0"
    }
}

struct RecentActivityRow: View {
    let title: String
    let subtitle: String
    let amount: String
    let status: String
    let statusColor: Color
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(amount)
                    .font(.numberSmall)
                    .foregroundColor(.textPrimary)
                
                Text(status)
                    .font(.hebrewSmall)
                    .foregroundColor(statusColor)
                    .padding(.horizontal, Spacing.sm)
                    .padding(.vertical, 2)
                    .background(statusColor.opacity(0.1))
                    .cornerRadius(4)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text(title)
                    .font(.hebrewBodyBold)
                    .foregroundColor(.textPrimary)
                
                Text(subtitle)
                    .font(.hebrewCaption)
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(Spacing.md)
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.md)
        .hebrewAlignment()
    }
}

// Placeholder views for other tabs
struct DocumentsView: View {
    var body: some View {
        NavigationView {
            Text("מסמכים")
                .navigationTitle("מסמכים")
        }
    }
}

struct ExpensesView: View {
    var body: some View {
        NavigationView {
            Text("הוצאות")
                .navigationTitle("הוצאות")
        }
    }
}

struct ReportsView: View {
    var body: some View {
        NavigationView {
            Text("דוחות")
                .navigationTitle("דוחות")
        }
    }
}

struct SettingsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        NavigationView {
            List {
                Section {
                    Button("התנתק") {
                        Task {
                            await authViewModel.signOut()
                        }
                    }
                    .foregroundColor(.accentRed)
                }
            }
            .navigationTitle("הגדרות")
        }
    }
}

#Preview {
    MainTabView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
