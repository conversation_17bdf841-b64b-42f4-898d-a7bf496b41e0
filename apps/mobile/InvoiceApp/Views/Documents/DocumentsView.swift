//
//  DocumentsView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct DocumentsView: View {
    @StateObject private var viewModel = DocumentsViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var selectedFilter: DocumentFilter = .all
    @State private var showingCreateDocument = false
    
    enum DocumentFilter: String, CaseIterable {
        case all = "הכל"
        case invoices = "חשבוניות"
        case receipts = "קבלות"
        case drafts = "טיוטות"
        case sent = "נשלחו"
        case paid = "שולמו"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Filter Tabs
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                            ForEach(DocumentFilter.allCases, id: \.self) { filter in
                                FilterTab(
                                    title: filter.rawValue,
                                    isSelected: selectedFilter == filter,
                                    isDark: themeManager.isDarkMode
                                ) {
                                    selectedFilter = filter
                                    Task {
                                        await viewModel.loadDocuments(filter: filter)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    }
                    .padding(.vertical, DesignSystem.Spacing.base.rawValue)
                    
                    // Documents List
                    if viewModel.isLoading {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: Color.dynamicPrimary(themeManager.isDarkMode)))
                        Spacer()
                    } else if viewModel.documents.isEmpty {
                        EmptyDocumentsView(
                            filter: selectedFilter,
                            isDark: themeManager.isDarkMode,
                            onCreateDocument: { showingCreateDocument = true }
                        )
                    } else {
                        ScrollView {
                            LazyVStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                                ForEach(viewModel.documents) { document in
                                    DocumentRowView(document: document, isDark: themeManager.isDarkMode)
                                        .onTapGesture {
                                            // Navigate to document detail
                                        }
                                }
                            }
                            .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        }
                        .refreshable {
                            await viewModel.refreshDocuments()
                        }
                    }
                }
            }
            .navigationTitle("מסמכים")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingCreateDocument = true }) {
                        Image(systemName: "plus")
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $showingCreateDocument) {
            CreateDocumentView()
        }
        .onAppear {
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadDocuments(companyId: companyId, filter: selectedFilter)
                }
            }
        }
    }
}

struct FilterTab: View {
    let title: String
    let isSelected: Bool
    let isDark: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.hebrewFont(size: .base, weight: isSelected ? .semibold : .medium))
                .foregroundColor(isSelected ? (isDark ? Color.backgroundPrimary : Color.foregroundLight) : Color.mutedForeground)
                .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                .padding(.vertical, DesignSystem.Spacing.sm.rawValue)
                .background(
                    isSelected ? Color.dynamicPrimary(isDark) : Color.clear
                )
                .cornerRadius(DesignSystem.Radius.full.rawValue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EmptyDocumentsView: View {
    let filter: DocumentsView.DocumentFilter
    let isDark: Bool
    let onCreateDocument: () -> Void
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: emptyStateIcon)
                    .font(.system(size: 60))
                    .foregroundColor(Color.mutedForeground)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text(emptyStateTitle)
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                        .multilineTextAlignment(.center)
                    
                    Text(emptyStateSubtitle)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
                
                if filter == .all || filter == .drafts {
                    Button(action: onCreateDocument) {
                        Text("צור מסמך ראשון")
                            .font(.hebrewFont(size: .large, weight: .semibold))
                            .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(Color.dynamicPrimary(isDark))
                            .cornerRadius(DesignSystem.Radius.base.rawValue)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.massive.rawValue)
                }
            }
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
    
    private var emptyStateIcon: String {
        switch filter {
        case .all:
            return "doc.text"
        case .invoices:
            return "doc.text.fill"
        case .receipts:
            return "receipt"
        case .drafts:
            return "doc.badge.plus"
        case .sent:
            return "paperplane"
        case .paid:
            return "checkmark.circle"
        }
    }
    
    private var emptyStateTitle: String {
        switch filter {
        case .all:
            return "אין מסמכים עדיין"
        case .invoices:
            return "אין חשבוניות עדיין"
        case .receipts:
            return "אין קבלות עדיין"
        case .drafts:
            return "אין טיוטות עדיין"
        case .sent:
            return "אין מסמכים שנשלחו"
        case .paid:
            return "אין מסמכים ששולמו"
        }
    }
    
    private var emptyStateSubtitle: String {
        switch filter {
        case .all:
            return "צור את המסמך הראשון שלך כדי להתחיל"
        case .invoices:
            return "צור חשבונית ראשונה כדי להתחיל"
        case .receipts:
            return "צור קבלה ראשונה כדי להתחיל"
        case .drafts:
            return "הטיוטות שלך יופיעו כאן"
        case .sent:
            return "מסמכים שנשלחו ללקוחות יופיעו כאן"
        case .paid:
            return "מסמכים ששולמו יופיעו כאן"
        }
    }
}

// MARK: - Documents ViewModel

@MainActor
class DocumentsViewModel: ObservableObject {
    @Published var documents: [Document] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadDocuments(companyId: UUID? = nil, filter: DocumentsView.DocumentFilter) async {
        guard let companyId = companyId else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let allDocuments = try await supabaseService.getDocuments(companyId: companyId, limit: 100)
            documents = filterDocuments(allDocuments, by: filter)
        } catch {
            errorMessage = "שגיאה בטעינת מסמכים: \(error.localizedDescription)"
            print("Documents loading error: \(error)")
        }
        
        isLoading = false
    }
    
    func refreshDocuments() async {
        // Implement refresh logic
    }
    
    private func filterDocuments(_ documents: [Document], by filter: DocumentsView.DocumentFilter) -> [Document] {
        switch filter {
        case .all:
            return documents
        case .invoices:
            return documents.filter { $0.documentType == .taxInvoice || $0.documentType == .taxInvoiceReceipt }
        case .receipts:
            return documents.filter { $0.documentType == .receipt }
        case .drafts:
            return documents.filter { $0.status == .draft }
        case .sent:
            return documents.filter { $0.status == .sent }
        case .paid:
            return documents.filter { $0.status == .paid }
        }
    }
}

struct CreateDocumentView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("יצירת מסמך חדש")
                    .font(.hebrewFont(size: .xl, weight: .semibold))
                
                Text("תכונה זו תהיה זמינה בקרוב")
                    .font(.hebrewFont(size: .base))
                    .foregroundColor(.secondary)
            }
            .navigationTitle("מסמך חדש")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

#Preview {
    DocumentsView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
