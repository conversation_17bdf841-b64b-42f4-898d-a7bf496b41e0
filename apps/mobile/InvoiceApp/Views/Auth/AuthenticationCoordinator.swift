//
//  AuthenticationCoordinator.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import Combine

// MARK: - Authentication Coordinator

@MainActor
class AuthenticationCoordinator: ObservableObject {
    @Published var authState: AuthState = .unauthenticated
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let authViewModel: AuthViewModel
    private let biometricManager = BiometricAuthManager.shared
    private let keychain = KeychainHelper.shared
    private var cancellables = Set<AnyCancellable>()
    
    enum AuthState {
        case unauthenticated
        case authenticating
        case authenticated(User, Company)
        case biometricRequired
        case sessionExpired
    }
    
    init(authViewModel: AuthViewModel) {
        self.authViewModel = authViewModel
        setupBindings()
        checkStoredCredentials()
    }
    
    private func setupBindings() {
        authViewModel.$isAuthenticated
            .combineLatest(authViewModel.$currentUser, authViewModel.$currentCompany)
            .sink { [weak self] isAuthenticated, user, company in
                if isAuthenticated, let user = user, let company = company {
                    self?.authState = .authenticated(user, company)
                } else {
                    self?.authState = .unauthenticated
                }
            }
            .store(in: &cancellables)
        
        authViewModel.$isLoading
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        authViewModel.$errorMessage
            .assign(to: \.errorMessage, on: self)
            .store(in: &cancellables)
    }
    
    private func checkStoredCredentials() {
        // Check if biometric auth is enabled and available
        if EnvironmentManager.shared.biometricAuthEnabled && biometricManager.isBiometricAvailable {
            authState = .biometricRequired
        } else {
            // Check for stored session
            Task {
                await authViewModel.checkAuthenticationStatus()
            }
        }
    }
    
    func authenticateWithBiometrics() async {
        do {
            let success = try await biometricManager.authenticateWithBiometrics()
            if success {
                await authViewModel.checkAuthenticationStatus()
            }
        } catch {
            errorMessage = "זיהוי ביומטרי נכשל"
            authState = .unauthenticated
        }
    }
    
    func signIn(email: String, password: String, rememberMe: Bool = false) async {
        authState = .authenticating
        
        await authViewModel.signIn(email: email, password: password)
        
        if authViewModel.isAuthenticated && rememberMe {
            // Store credentials securely for biometric auth
            storeCredentialsForBiometricAuth(email: email)
        }
    }
    
    func signUp(registrationData: RegistrationData) async {
        authState = .authenticating
        await authViewModel.signUp(registrationData: registrationData)
    }
    
    func signOut() async {
        await authViewModel.signOut()
        clearStoredCredentials()
        authState = .unauthenticated
    }
    
    private func storeCredentialsForBiometricAuth(email: String) {
        let credentialsData = email.data(using: .utf8) ?? Data()
        _ = keychain.save(credentialsData, for: "stored_email")
    }
    
    private func clearStoredCredentials() {
        _ = keychain.delete(for: "stored_email")
    }
}

// MARK: - Enhanced Authentication View

struct EnhancedAuthenticationView: View {
    @StateObject private var coordinator: AuthenticationCoordinator
    @EnvironmentObject var themeManager: ThemeManager
    
    init(authViewModel: AuthViewModel) {
        self._coordinator = StateObject(wrappedValue: AuthenticationCoordinator(authViewModel: authViewModel))
    }
    
    var body: some View {
        ZStack {
            Color.dynamicBackground(themeManager.isDarkMode)
                .ignoresSafeArea()
            
            CosmicGridBackground(isDark: themeManager.isDarkMode)
            
            switch coordinator.authState {
            case .unauthenticated:
                AuthenticationOptionsView(coordinator: coordinator)
            case .authenticating:
                AuthenticatingView()
            case .authenticated(let user, let company):
                AuthenticatedView(user: user, company: company)
            case .biometricRequired:
                BiometricAuthView(coordinator: coordinator)
            case .sessionExpired:
                SessionExpiredView(coordinator: coordinator)
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct AuthenticationOptionsView: View {
    @ObservedObject var coordinator: AuthenticationCoordinator
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingLogin = false
    @State private var showingSignUp = false
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            // App Logo and Title
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 80, weight: .light))
                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    .cosmicGlow(isDark: themeManager.isDarkMode)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("מערכת חשבוניות")
                        .font(.hebrewFont(size: .hero, weight: .bold))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                        .multilineTextAlignment(.center)
                    
                    Text("ניהול חשבוניות והוצאות חכם")
                        .font(.hebrewFont(size: .large, weight: .medium))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
            }
            
            Spacer()
            
            // Authentication Buttons
            VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                PrimaryButton(
                    title: "התחבר",
                    isDark: themeManager.isDarkMode
                ) {
                    showingLogin = true
                    HapticManager.shared.selection()
                }
                
                SecondaryButton(
                    title: "הירשם",
                    isDark: themeManager.isDarkMode
                ) {
                    showingSignUp = true
                    HapticManager.shared.selection()
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
            
            Spacer()
            
            // App Version
            Text("גרסה \(EnvironmentManager.shared.appVersion)")
                .font(.hebrewFont(size: .small))
                .foregroundColor(Color.mutedForeground)
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
        .sheet(isPresented: $showingLogin) {
            EnhancedLoginView(coordinator: coordinator)
        }
        .sheet(isPresented: $showingSignUp) {
            EnhancedSignUpView(coordinator: coordinator)
        }
    }
}

struct BiometricAuthView: View {
    @ObservedObject var coordinator: AuthenticationCoordinator
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: biometricIcon)
                    .font(.system(size: 80))
                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    .cosmicGlow(isDark: themeManager.isDarkMode)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("זיהוי ביומטרי")
                        .font(.hebrewFont(size: .xxxl, weight: .bold))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Text("השתמש ב\(biometricTypeText) כדי לגשת לחשבון שלך")
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
            }
            
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                PrimaryButton(
                    title: "השתמש ב\(biometricTypeText)",
                    isLoading: coordinator.isLoading,
                    isDark: themeManager.isDarkMode
                ) {
                    Task {
                        await coordinator.authenticateWithBiometrics()
                    }
                }
                
                SecondaryButton(
                    title: "השתמש בסיסמה",
                    isDark: themeManager.isDarkMode
                ) {
                    coordinator.authState = .unauthenticated
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
    
    private var biometricIcon: String {
        switch BiometricAuthManager.shared.biometryType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        default:
            return "lock.fill"
        }
    }
    
    private var biometricTypeText: String {
        switch BiometricAuthManager.shared.biometryType {
        case .faceID:
            return "Face ID"
        case .touchID:
            return "Touch ID"
        default:
            return "זיהוי ביומטרי"
        }
    }
}

struct AuthenticatingView: View {
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        LoadingView(
            message: "מתחבר...",
            isDark: themeManager.isDarkMode
        )
    }
}

struct AuthenticatedView: View {
    let user: User
    let company: Company
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(Color.success)
                    .cosmicGlow(isDark: themeManager.isDarkMode)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("התחברת בהצלחה!")
                        .font(.hebrewFont(size: .xxxl, weight: .bold))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Text("שלום, \(user.fullName)")
                        .font(.hebrewFont(size: .large))
                        .foregroundColor(Color.mutedForeground)
                    
                    Text(company.nameHebrew)
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                }
            }
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
        .onAppear {
            HapticManager.shared.notification(.success)
        }
    }
}

struct SessionExpiredView: View {
    @ObservedObject var coordinator: AuthenticationCoordinator
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: "clock.badge.exclamationmark.fill")
                    .font(.system(size: 80))
                    .foregroundColor(Color.warning)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("פג תוקף ההתחברות")
                        .font(.hebrewFont(size: .xxxl, weight: .bold))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Text("אנא התחבר שוב כדי להמשיך")
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
            }
            
            Spacer()
            
            PrimaryButton(
                title: "התחבר שוב",
                isDark: themeManager.isDarkMode
            ) {
                coordinator.authState = .unauthenticated
            }
            .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
}

// MARK: - Enhanced Login View

struct EnhancedLoginView: View {
    @ObservedObject var coordinator: AuthenticationCoordinator
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var email = ""
    @State private var password = ""
    @State private var rememberMe = false
    @StateObject private var formState = FormStateManager()
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                CosmicGridBackground(isDark: themeManager.isDarkMode)
                
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                        // Header
                        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                            Image(systemName: "person.circle.fill")
                                .font(.system(size: 60, weight: .light))
                                .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                                .cosmicGlow(isDark: themeManager.isDarkMode)
                            
                            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                                Text("התחברות")
                                    .font(.hebrewFont(size: .xxxl, weight: .bold))
                                    .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                                
                                Text("הכנס את פרטי החשבון שלך")
                                    .font(.hebrewFont(size: .base, weight: .medium))
                                    .foregroundColor(Color.mutedForeground)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.top, DesignSystem.Spacing.massive.rawValue)
                        
                        // Login Form
                        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                                InputField(
                                    title: "כתובת אימייל",
                                    text: $email,
                                    placeholder: "<EMAIL>",
                                    keyboardType: .emailAddress,
                                    isDark: themeManager.isDarkMode,
                                    validation: formState.getValidationResult(for: "email")
                                )
                                .onChange(of: email) { newValue in
                                    formState.validate(field: "email", value: newValue, validator: Validator.validateEmail)
                                }
                                
                                InputField(
                                    title: "סיסמה",
                                    text: $password,
                                    placeholder: "••••••••",
                                    isSecure: true,
                                    isDark: themeManager.isDarkMode,
                                    validation: formState.getValidationResult(for: "password")
                                )
                                .onChange(of: password) { newValue in
                                    formState.validate(field: "password", value: newValue) { pwd in
                                        pwd.isEmpty ? .invalid("סיסמה נדרשת") : .valid
                                    }
                                }
                                
                                if BiometricAuthManager.shared.isBiometricAvailable {
                                    ToggleField(
                                        title: "זכור אותי",
                                        subtitle: "השתמש בזיהוי ביומטרי בפעם הבאה",
                                        isOn: $rememberMe,
                                        isDark: themeManager.isDarkMode
                                    )
                                }
                            }
                            
                            // Error Message
                            if let errorMessage = coordinator.errorMessage {
                                Text(errorMessage)
                                    .font(.hebrewFont(size: .small, weight: .medium))
                                    .foregroundColor(Color.destructive)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }
                            
                            // Login Button
                            PrimaryButton(
                                title: "התחבר",
                                isLoading: coordinator.isLoading,
                                isEnabled: formState.isFormValid,
                                isDark: themeManager.isDarkMode
                            ) {
                                Task {
                                    await coordinator.signIn(email: email, password: password, rememberMe: rememberMe)
                                    if coordinator.authState == .authenticated(coordinator.authViewModel.currentUser!, coordinator.authViewModel.currentCompany!) {
                                        dismiss()
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        .cardStyle(isDark: themeManager.isDarkMode)
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        
                        Spacer(minLength: DesignSystem.Spacing.xl.rawValue)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Enhanced SignUp View Placeholder

struct EnhancedSignUpView: View {
    @ObservedObject var coordinator: AuthenticationCoordinator
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            SignUpView()
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("ביטול") {
                            dismiss()
                        }
                    }
                }
        }
    }
}

#Preview {
    EnhancedAuthenticationView(authViewModel: AuthViewModel())
        .environmentObject(ThemeManager())
}
