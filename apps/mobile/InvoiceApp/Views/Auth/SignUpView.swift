//
//  SignUpView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct SignUpView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentStep = 1
    @State private var progress: Double = 0.2
    
    // Step 1: Survey Data
    @State private var industry = ""
    @State private var annualRevenue = ""
    @State private var interestedInLoan = true
    @State private var interestedInInsurance = true
    @State private var interestedInAccounting = true
    
    // Step 2: Business Data
    @State private var companyId = ""
    @State private var nameHebrew = ""
    @State private var nameEnglish = ""
    @State private var addressHebrew = ""
    @State private var cityHebrew = ""
    @State private var businessPhone = ""
    
    // Step 3: Account Data
    @State private var email = ""
    @State private var password = ""
    @State private var fullName = ""
    @State private var phone = ""
    
    @FocusState private var focusedField: Field?
    
    enum Field {
        case industry, companyId, nameHebrew, nameEnglish, addressHebrew, cityHebrew, businessPhone
        case email, password, fullName, phone
    }
    
    let industries = ["טכנולוגיה", "קמעונאות", "שירותים", "ייצור", "בנייה", "בריאות", "אחר"]
    let revenueOptions = ["פחות מ-500 אלף", "500 אלף - מיליון", "מיליון - 5 מיליון", "5-10 מיליון", "מעל 10 מיליון"]
    
    var body: some View {
        ZStack {
            Color.dynamicBackground(themeManager.isDarkMode)
                .ignoresSafeArea()
            
            CosmicGridBackground(isDark: themeManager.isDarkMode)
            
            VStack(spacing: 0) {
                // Header with Progress
                VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                    Text("הרשמה")
                        .font(.hebrewFont(size: .xxxl, weight: .bold))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Text("צור חשבון חדש למערכת החשבוניות")
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                    
                    // Progress Bar
                    VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                        ProgressView(value: progress)
                            .progressViewStyle(LinearProgressViewStyle(tint: Color.dynamicPrimary(themeManager.isDarkMode)))
                            .scaleEffect(x: 1, y: 2, anchor: .center)
                        
                        Text("שלב \(currentStep) מתוך 3")
                            .font(.hebrewFont(size: .small))
                            .foregroundColor(Color.mutedForeground)
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
                .padding(.top, DesignSystem.Spacing.lg.rawValue)
                
                // Step Content
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                        Group {
                            switch currentStep {
                            case 1:
                                surveyStep()
                            case 2:
                                businessStep()
                            case 3:
                                accountStep()
                            default:
                                EmptyView()
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        .cardStyle(isDark: themeManager.isDarkMode)
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        
                        Spacer(minLength: 100) // Space for navigation buttons
                    }
                    .padding(.top, DesignSystem.Spacing.xl.rawValue)
                }
                
                // Navigation Buttons
                VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                    if let errorMessage = authViewModel.errorMessage {
                        Text(errorMessage)
                            .font(.hebrewFont(size: .small, weight: .medium))
                            .foregroundColor(Color.destructive)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    
                    HStack(spacing: DesignSystem.Spacing.base.rawValue) {
                        if currentStep > 1 {
                            Button("הקודם") {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    currentStep -= 1
                                    updateProgress()
                                }
                            }
                            .buttonStyle(SecondaryButtonStyle(isDark: themeManager.isDarkMode))
                        }
                        
                        Spacer()
                        
                        Button(currentStep == 3 ? "הירשם" : "הבא") {
                            if currentStep == 3 {
                                handleSignUp()
                            } else {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    currentStep += 1
                                    updateProgress()
                                }
                            }
                        }
                        .buttonStyle(PrimaryButtonStyle(
                            isDark: themeManager.isDarkMode,
                            isLoading: authViewModel.isLoading,
                            isEnabled: isCurrentStepValid
                        ))
                        .disabled(!isCurrentStepValid || authViewModel.isLoading)
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
                .padding(.bottom, DesignSystem.Spacing.xl.rawValue)
                .background(Color.dynamicBackground(themeManager.isDarkMode))
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("ביטול") {
                    dismiss()
                }
                .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            updateProgress()
        }
    }
    
    // MARK: - Step Views
    
    @ViewBuilder
    private func surveyStep() -> some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            Text("ספר לנו על העסק שלך")
                .font(.hebrewFont(size: .xl, weight: .semibold))
                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                // Industry Picker
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("תחום עיסוק")
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Menu {
                        ForEach(industries, id: \.self) { option in
                            Button(option) {
                                industry = option
                            }
                        }
                    } label: {
                        HStack {
                            Image(systemName: "chevron.down")
                                .foregroundColor(Color.mutedForeground)
                            
                            Spacer()
                            
                            Text(industry.isEmpty ? "בחר תחום עיסוק" : industry)
                                .font(.hebrewFont(size: .base))
                                .foregroundColor(industry.isEmpty ? Color.mutedForeground : Color.dynamicForeground(themeManager.isDarkMode))
                        }
                        .padding(DesignSystem.Spacing.base.rawValue)
                        .background(Color.dynamicCard(themeManager.isDarkMode))
                        .cornerRadius(DesignSystem.Radius.base.rawValue)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                                .stroke(Color.dynamicBorder(themeManager.isDarkMode), lineWidth: 1)
                        )
                    }
                }
                
                // Annual Revenue Picker
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("מחזור שנתי (אופציונלי)")
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    Menu {
                        ForEach(revenueOptions, id: \.self) { option in
                            Button(option) {
                                annualRevenue = option
                            }
                        }
                    } label: {
                        HStack {
                            Image(systemName: "chevron.down")
                                .foregroundColor(Color.mutedForeground)
                            
                            Spacer()
                            
                            Text(annualRevenue.isEmpty ? "בחר מחזור שנתי" : annualRevenue)
                                .font(.hebrewFont(size: .base))
                                .foregroundColor(annualRevenue.isEmpty ? Color.mutedForeground : Color.dynamicForeground(themeManager.isDarkMode))
                        }
                        .padding(DesignSystem.Spacing.base.rawValue)
                        .background(Color.dynamicCard(themeManager.isDarkMode))
                        .cornerRadius(DesignSystem.Radius.base.rawValue)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                                .stroke(Color.dynamicBorder(themeManager.isDarkMode), lineWidth: 1)
                        )
                    }
                }
                
                // Interest Questions
                VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                    Text("האם אתה מעוניין בשירותים הבאים?")
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    InterestToggle(
                        title: "הלוואות עסקיות",
                        isOn: $interestedInLoan,
                        isDark: themeManager.isDarkMode
                    )
                    
                    InterestToggle(
                        title: "ביטוח עסקי",
                        isOn: $interestedInInsurance,
                        isDark: themeManager.isDarkMode
                    )
                    
                    InterestToggle(
                        title: "שירותי הנהלת חשבונות",
                        isOn: $interestedInAccounting,
                        isDark: themeManager.isDarkMode
                    )
                }
            }
        }
    }
    
    @ViewBuilder
    private func businessStep() -> some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            Text("פרטי העסק")
                .font(.hebrewFont(size: .xl, weight: .semibold))
                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                FormField(
                    title: "ע.מ / ח.פ",
                    text: $companyId,
                    placeholder: "*********",
                    keyboardType: .numberPad,
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .companyId)
                
                FormField(
                    title: "שם החברה (עברית)",
                    text: $nameHebrew,
                    placeholder: "שם החברה",
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .nameHebrew)
                
                FormField(
                    title: "שם החברה (אנגלית) - אופציונלי",
                    text: $nameEnglish,
                    placeholder: "Company Name",
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .nameEnglish)
                
                FormField(
                    title: "כתובת (עברית)",
                    text: $addressHebrew,
                    placeholder: "רחוב 123, תל אביב",
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .addressHebrew)
                
                FormField(
                    title: "עיר (עברית)",
                    text: $cityHebrew,
                    placeholder: "תל אביב",
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .cityHebrew)
                
                FormField(
                    title: "טלפון עסק",
                    text: $businessPhone,
                    placeholder: "03-1234567",
                    keyboardType: .phonePad,
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .businessPhone)
            }
        }
    }
    
    @ViewBuilder
    private func accountStep() -> some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            Text("פרטי החשבון")
                .font(.hebrewFont(size: .xl, weight: .semibold))
                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                FormField(
                    title: "שם מלא",
                    text: $fullName,
                    placeholder: "יוחנן כהן",
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .fullName)
                
                FormField(
                    title: "כתובת אימייל",
                    text: $email,
                    placeholder: "<EMAIL>",
                    keyboardType: .emailAddress,
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .email)
                .textContentType(.emailAddress)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                
                FormField(
                    title: "טלפון נייד",
                    text: $phone,
                    placeholder: "050-1234567",
                    keyboardType: .phonePad,
                    isDark: themeManager.isDarkMode
                )
                .focused($focusedField, equals: .phone)
                
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text("סיסמה")
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                    
                    SecureField("לפחות 8 תווים", text: $password)
                        .font(.latinFont(size: .base))
                        .padding(DesignSystem.Spacing.base.rawValue)
                        .background(Color.dynamicCard(themeManager.isDarkMode))
                        .cornerRadius(DesignSystem.Radius.base.rawValue)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                                .stroke(Color.dynamicBorder(themeManager.isDarkMode), lineWidth: 1)
                        )
                        .multilineTextAlignment(.trailing)
                        .focused($focusedField, equals: .password)
                        .textContentType(.newPassword)
                }
            }
        }
    }

    // MARK: - Helper Methods

    private var isCurrentStepValid: Bool {
        switch currentStep {
        case 1:
            return !industry.isEmpty
        case 2:
            return !companyId.isEmpty && !nameHebrew.isEmpty && !addressHebrew.isEmpty && !cityHebrew.isEmpty && !businessPhone.isEmpty
        case 3:
            return !fullName.isEmpty && !email.isEmpty && !phone.isEmpty && !password.isEmpty && email.contains("@") && password.count >= 8
        default:
            return false
        }
    }

    private func updateProgress() {
        switch currentStep {
        case 1:
            progress = 0.33
        case 2:
            progress = 0.66
        case 3:
            progress = 1.0
        default:
            progress = 0.33
        }
    }

    private func handleSignUp() {
        guard isCurrentStepValid else { return }

        let registrationData = RegistrationData(
            email: email,
            password: password,
            fullName: fullName,
            phone: phone,
            company: CompanyRegistrationData(
                businessNumber: companyId,
                nameHebrew: nameHebrew,
                nameEnglish: nameEnglish.isEmpty ? nil : nameEnglish,
                addressHebrew: addressHebrew,
                cityHebrew: cityHebrew,
                phone: businessPhone,
                industry: industry,
                annualRevenue: annualRevenue.isEmpty ? nil : annualRevenue,
                interestedInLoan: interestedInLoan,
                interestedInInsurance: interestedInInsurance,
                interestedInAccounting: interestedInAccounting
            )
        )

        Task {
            await authViewModel.signUp(registrationData: registrationData)
        }
    }
}

// MARK: - Supporting Views

struct FormField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isDark: Bool

    init(title: String, text: Binding<String>, placeholder: String, keyboardType: UIKeyboardType = .default, isDark: Bool) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isDark = isDark
    }

    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            Text(title)
                .font(.hebrewFont(size: .base, weight: .medium))
                .foregroundColor(Color.dynamicForeground(isDark))

            TextField(placeholder, text: $text)
                .font(.hebrewFont(size: .base))
                .padding(DesignSystem.Spacing.base.rawValue)
                .background(Color.dynamicCard(isDark))
                .cornerRadius(DesignSystem.Radius.base.rawValue)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                        .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
                )
                .keyboardType(keyboardType)
                .multilineTextAlignment(.trailing)
        }
    }
}

struct InterestToggle: View {
    let title: String
    @Binding var isOn: Bool
    let isDark: Bool

    var body: some View {
        HStack {
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.dynamicPrimary(isDark)))

            Spacer()

            Text(title)
                .font(.hebrewFont(size: .base))
                .foregroundColor(Color.dynamicForeground(isDark))
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .background(Color.dynamicCard(isDark))
        .cornerRadius(DesignSystem.Radius.base.rawValue)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
        )
    }
}

struct PrimaryButtonStyle: ButtonStyle {
    let isDark: Bool
    let isLoading: Bool
    let isEnabled: Bool

    func makeBody(configuration: Configuration) -> some View {
        HStack {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: isDark ? Color.backgroundPrimary : Color.foregroundLight))
                    .scaleEffect(0.8)
            }

            configuration.label
                .font(.hebrewFont(size: .large, weight: .semibold))
                .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
        }
        .frame(height: 56)
        .frame(maxWidth: .infinity)
        .background(Color.dynamicPrimary(isDark))
        .cornerRadius(DesignSystem.Radius.base.rawValue)
        .opacity(isEnabled ? 1.0 : 0.6)
        .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    let isDark: Bool

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.hebrewFont(size: .large, weight: .semibold))
            .foregroundColor(Color.dynamicForeground(isDark))
            .frame(height: 56)
            .frame(maxWidth: .infinity)
            .background(Color.clear)
            .cornerRadius(DesignSystem.Radius.base.rawValue)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                    .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    NavigationView {
        SignUpView()
            .environmentObject(AuthViewModel())
            .environmentObject(ThemeManager())
    }
}
