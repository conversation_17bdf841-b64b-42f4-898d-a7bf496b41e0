//
//  LoginView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct LoginView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var email = ""
    @State private var password = ""
    @State private var showPassword = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case email, password
    }
    
    var body: some View {
        ZStack {
            // Background
            Color.dynamicBackground(themeManager.isDarkMode)
                .ignoresSafeArea()
            
            CosmicGridBackground(isDark: themeManager.isDarkMode)
            
            ScrollView {
                VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                    // Header
                    VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                        Image(systemName: "person.circle.fill")
                            .font(.system(size: 60, weight: .light))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            .cosmicGlow(isDark: themeManager.isDarkMode)
                        
                        VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                            Text("התחברות")
                                .font(.hebrewFont(size: .xxxl, weight: .bold))
                                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                            
                            Text("הכנס את פרטי החשבון שלך")
                                .font(.hebrewFont(size: .base, weight: .medium))
                                .foregroundColor(Color.mutedForeground)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top, DesignSystem.Spacing.massive.rawValue)
                    
                    // Login Form
                    VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                            // Email Field
                            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
                                Text("כתובת אימייל")
                                    .font(.hebrewFont(size: .base, weight: .medium))
                                    .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                                
                                CustomTextField(
                                    text: $email,
                                    placeholder: "<EMAIL>",
                                    keyboardType: .emailAddress,
                                    isDark: themeManager.isDarkMode
                                )
                                .focused($focusedField, equals: .email)
                                .textContentType(.emailAddress)
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                            }
                            
                            // Password Field
                            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
                                Text("סיסמה")
                                    .font(.hebrewFont(size: .base, weight: .medium))
                                    .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                                
                                CustomSecureField(
                                    text: $password,
                                    placeholder: "••••••••",
                                    showPassword: $showPassword,
                                    isDark: themeManager.isDarkMode
                                )
                                .focused($focusedField, equals: .password)
                                .textContentType(.password)
                            }
                        }
                        
                        // Error Message
                        if let errorMessage = authViewModel.errorMessage {
                            Text(errorMessage)
                                .font(.hebrewFont(size: .small, weight: .medium))
                                .foregroundColor(Color.destructive)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        
                        // Login Button
                        Button(action: handleLogin) {
                            HStack {
                                if authViewModel.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: Color.backgroundPrimary))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(authViewModel.isLoading ? "מתחבר..." : "התחבר")
                                    .font(.hebrewFont(size: .large, weight: .semibold))
                                    .foregroundColor(themeManager.isDarkMode ? Color.backgroundPrimary : Color.foregroundLight)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(Color.dynamicPrimary(themeManager.isDarkMode))
                            .cornerRadius(DesignSystem.Radius.base.rawValue)
                            .cosmicGlow(isDark: themeManager.isDarkMode)
                        }
                        .disabled(authViewModel.isLoading || !isFormValid)
                        .opacity(isFormValid ? 1.0 : 0.6)
                        
                        // Sign Up Link
                        HStack {
                            Text("אין לך חשבון?")
                                .font(.hebrewFont(size: .base))
                                .foregroundColor(Color.mutedForeground)
                            
                            NavigationLink(destination: SignUpView()) {
                                Text("הירשם כאן")
                                    .font(.hebrewFont(size: .base, weight: .semibold))
                                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            }
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
                    .cardStyle(isDark: themeManager.isDarkMode)
                    .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    
                    Spacer(minLength: DesignSystem.Spacing.xl.rawValue)
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("ביטול") {
                    dismiss()
                }
                .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onSubmit {
            if focusedField == .email {
                focusedField = .password
            } else if focusedField == .password {
                handleLogin()
            }
        }
    }
    
    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && email.contains("@")
    }
    
    private func handleLogin() {
        guard isFormValid else { return }
        
        Task {
            await authViewModel.signIn(email: email, password: password)
        }
    }
}

struct CustomTextField: View {
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isDark: Bool
    
    var body: some View {
        TextField(placeholder, text: $text)
            .font(.latinFont(size: .base))
            .padding(DesignSystem.Spacing.base.rawValue)
            .background(Color.dynamicCard(isDark))
            .cornerRadius(DesignSystem.Radius.base.rawValue)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                    .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
            )
            .keyboardType(keyboardType)
            .multilineTextAlignment(.trailing)
    }
}

struct CustomSecureField: View {
    @Binding var text: String
    let placeholder: String
    @Binding var showPassword: Bool
    let isDark: Bool
    
    var body: some View {
        HStack {
            Button(action: { showPassword.toggle() }) {
                Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                    .foregroundColor(Color.mutedForeground)
            }
            
            if showPassword {
                TextField(placeholder, text: $text)
                    .font(.latinFont(size: .base))
                    .multilineTextAlignment(.trailing)
            } else {
                SecureField(placeholder, text: $text)
                    .font(.latinFont(size: .base))
                    .multilineTextAlignment(.trailing)
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .background(Color.dynamicCard(isDark))
        .cornerRadius(DesignSystem.Radius.base.rawValue)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
        )
    }
}

#Preview {
    NavigationView {
        LoginView()
            .environmentObject(AuthViewModel())
            .environmentObject(ThemeManager())
    }
}
