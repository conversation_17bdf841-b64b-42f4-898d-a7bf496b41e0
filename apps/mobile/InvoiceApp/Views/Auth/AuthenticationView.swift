//
//  AuthenticationView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct AuthenticationView: View {
    @State private var showingSignUp = false
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background with cosmic grid pattern
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                CosmicGridBackground(isDark: themeManager.isDarkMode)
                
                VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                    Spacer()
                    
                    // Logo and Title
                    VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                        // App Logo
                        Image(systemName: "doc.text.fill")
                            .font(.system(size: 60, weight: .light))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            .cosmicGlow(isDark: themeManager.isDarkMode)
                        
                        VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                            Text("מערכת חשבוניות")
                                .font(.hebrewFont(size: .hero, weight: .bold))
                                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                                .multilineTextAlignment(.center)
                            
                            Text("ניהול חשבוניות והוצאות חכם")
                                .font(.hebrewFont(size: .large, weight: .medium))
                                .foregroundColor(Color.dynamicMuted(themeManager.isDarkMode))
                                .multilineTextAlignment(.center)
                        }
                    }
                    
                    Spacer()
                    
                    // Authentication Buttons
                    VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                        NavigationLink(destination: LoginView()) {
                            AuthButton(
                                title: "התחבר",
                                style: .primary,
                                isDark: themeManager.isDarkMode
                            )
                        }
                        
                        NavigationLink(destination: SignUpView()) {
                            AuthButton(
                                title: "הירשם",
                                style: .secondary,
                                isDark: themeManager.isDarkMode
                            )
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.xl.rawValue)
                    
                    Spacer()
                }
                .padding(DesignSystem.Spacing.xl.rawValue)
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct AuthButton: View {
    let title: String
    let style: AuthButtonStyle
    let isDark: Bool
    
    enum AuthButtonStyle {
        case primary
        case secondary
    }
    
    var body: some View {
        HStack {
            Spacer()
            Text(title)
                .font(.hebrewFont(size: .large, weight: .semibold))
                .foregroundColor(foregroundColor)
            Spacer()
        }
        .frame(height: 56)
        .background(backgroundColor)
        .cornerRadius(DesignSystem.Radius.base.rawValue)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                .stroke(borderColor, lineWidth: style == .secondary ? 1 : 0)
        )
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.2), value: style)
    }
    
    private var backgroundColor: Color {
        switch style {
        case .primary:
            return Color.dynamicPrimary(isDark)
        case .secondary:
            return Color.clear
        }
    }
    
    private var foregroundColor: Color {
        switch style {
        case .primary:
            return isDark ? Color.backgroundPrimary : Color.foregroundLight
        case .secondary:
            return Color.dynamicForeground(isDark)
        }
    }
    
    private var borderColor: Color {
        return Color.dynamicBorder(isDark)
    }
}

struct CosmicGridBackground: View {
    let isDark: Bool
    
    var body: some View {
        Canvas { context, size in
            let gridSize: CGFloat = 30
            let lineWidth: CGFloat = 1
            let opacity: Double = isDark ? 0.05 : 0.03
            
            context.stroke(
                Path { path in
                    // Vertical lines
                    for x in stride(from: 0, through: size.width, by: gridSize) {
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: size.height))
                    }
                    
                    // Horizontal lines
                    for y in stride(from: 0, through: size.height, by: gridSize) {
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: size.width, y: y))
                    }
                },
                with: .color(Color.dynamicForeground(isDark).opacity(opacity)),
                lineWidth: lineWidth
            )
        }
        .ignoresSafeArea()
    }
}

#Preview {
    AuthenticationView()
        .environmentObject(ThemeManager())
}
