//
//  NotificationSettingsView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @StateObject private var viewModel = NotificationSettingsViewModel()
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var pushNotificationService: PushNotificationService
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        ZStack {
            Color.dynamicBackground(themeManager.isDarkMode)
                .ignoresSafeArea()
            
            ScrollView {
                LazyVStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                    // Push Notifications Status
                    pushNotificationStatusSection
                    
                    // Notification Preferences
                    if pushNotificationService.authorizationStatus == .authorized {
                        notificationPreferencesSection
                    }
                    
                    // Test Notifications (Debug only)
                    if EnvironmentManager.shared.isDebugMode {
                        testNotificationsSection
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                .padding(.top, DesignSystem.Spacing.base.rawValue)
            }
        }
        .navigationTitle("הגדרות התראות")
        .navigationBarTitleDisplayMode(.large)
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            if let userId = authViewModel.currentUser?.id {
                Task {
                    await viewModel.loadNotificationPreferences(userId: userId)
                }
            }
        }
    }
    
    // MARK: - Push Notification Status Section
    
    private var pushNotificationStatusSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "סטטוס התראות",
                isDark: themeManager.isDarkMode
            )
            
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                // Authorization Status
                HStack {
                    statusIndicator
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("התראות דחיפה")
                            .font(.hebrewFont(size: .base, weight: .medium))
                            .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                        
                        Text(authorizationStatusText)
                            .font(.hebrewFont(size: .small))
                            .foregroundColor(authorizationStatusColor)
                    }
                }
                .padding(DesignSystem.Spacing.base.rawValue)
                .cardStyle(isDark: themeManager.isDarkMode)
                
                // Enable Button
                if pushNotificationService.authorizationStatus == .denied {
                    Button(action: openAppSettings) {
                        Text("פתח הגדרות אפליקציה")
                            .font(.hebrewFont(size: .base, weight: .medium))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(Color.clear)
                            .overlay(
                                RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                                    .stroke(Color.dynamicPrimary(themeManager.isDarkMode), lineWidth: 1)
                            )
                    }
                } else if pushNotificationService.authorizationStatus == .notDetermined {
                    PrimaryButton(
                        title: "אפשר התראות",
                        isDark: themeManager.isDarkMode
                    ) {
                        Task {
                            await pushNotificationService.requestPermission()
                        }
                    }
                }
                
                // Registration Status
                if pushNotificationService.authorizationStatus == .authorized {
                    HStack {
                        Image(systemName: pushNotificationService.isRegistered ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(pushNotificationService.isRegistered ? Color.success : Color.destructive)
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 2) {
                            Text("רישום במערכת")
                                .font(.hebrewFont(size: .base, weight: .medium))
                                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                            
                            Text(pushNotificationService.isRegistered ? "רשום בהצלחה" : "לא רשום")
                                .font(.hebrewFont(size: .small))
                                .foregroundColor(pushNotificationService.isRegistered ? Color.success : Color.destructive)
                        }
                    }
                    .padding(DesignSystem.Spacing.base.rawValue)
                    .cardStyle(isDark: themeManager.isDarkMode)
                }
            }
        }
    }
    
    // MARK: - Notification Preferences Section
    
    private var notificationPreferencesSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "העדפות התראות",
                isDark: themeManager.isDarkMode
            )
            
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                ToggleField(
                    title: "אישור הוצאות",
                    subtitle: "התראה כאשר יש הוצאה חדשה לאישור",
                    isOn: $viewModel.expenseApprovals,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.expenseApprovals) { _ in
                    savePreferences()
                }
                
                ToggleField(
                    title: "תזכורות חשבוניות",
                    subtitle: "התראה על חשבוניות באיחור או שטרם שולמו",
                    isOn: $viewModel.invoiceReminders,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.invoiceReminders) { _ in
                    savePreferences()
                }
                
                ToggleField(
                    title: "תזכורות מע״מ",
                    subtitle: "התראה לפני מועדי תשלום מע״מ",
                    isOn: $viewModel.vatReminders,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.vatReminders) { _ in
                    savePreferences()
                }
                
                ToggleField(
                    title: "התראות תשלום",
                    subtitle: "התראה כאשר מתקבל תשלום",
                    isOn: $viewModel.paymentNotifications,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.paymentNotifications) { _ in
                    savePreferences()
                }
                
                ToggleField(
                    title: "עדכוני מערכת",
                    subtitle: "התראות על עדכונים חשובים במערכת",
                    isOn: $viewModel.systemUpdates,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.systemUpdates) { _ in
                    savePreferences()
                }
                
                ToggleField(
                    title: "עדכוני שיווק",
                    subtitle: "התראות על תכונות חדשות והצעות מיוחדות",
                    isOn: $viewModel.marketingNotifications,
                    isDark: themeManager.isDarkMode
                )
                .onChange(of: viewModel.marketingNotifications) { _ in
                    savePreferences()
                }
            }
        }
    }
    
    // MARK: - Test Notifications Section (Debug)
    
    private var testNotificationsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "בדיקת התראות (Debug)",
                isDark: themeManager.isDarkMode
            )
            
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                SecondaryButton(
                    title: "בדוק התראה מקומית",
                    isDark: themeManager.isDarkMode
                ) {
                    Task {
                        await pushNotificationService.scheduleLocalNotification(
                            title: "בדיקת התראה",
                            body: "זוהי התראה לבדיקה",
                            identifier: "test_notification",
                            timeInterval: 2
                        )
                    }
                }
                
                SecondaryButton(
                    title: "בדוק התראת הוצאה",
                    isDark: themeManager.isDarkMode
                ) {
                    Task {
                        await pushNotificationService.scheduleLocalNotification(
                            title: "הוצאה חדשה לאישור",
                            body: "הוצאה של 150 ₪ מסופר פארם ממתינה לאישור",
                            identifier: "test_expense",
                            userInfo: [
                                "expense_id": UUID().uuidString,
                                "type": "expense_approval"
                            ]
                        )
                    }
                }
                
                SecondaryButton(
                    title: "בדוק תזכורת מע״מ",
                    isDark: themeManager.isDarkMode
                ) {
                    Task {
                        await pushNotificationService.scheduleLocalNotification(
                            title: "תזכורת מע״מ",
                            body: "מועד תשלום המע״מ בעוד 7 ימים (15/02/2025)",
                            identifier: "test_vat",
                            userInfo: [
                                "type": "vat_reminder",
                                "screen": "reports"
                            ]
                        )
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var statusIndicator: some View {
        Image(systemName: statusIcon)
            .foregroundColor(authorizationStatusColor)
            .font(.title2)
    }
    
    private var statusIcon: String {
        switch pushNotificationService.authorizationStatus {
        case .authorized:
            return "checkmark.circle.fill"
        case .denied:
            return "xmark.circle.fill"
        case .notDetermined:
            return "questionmark.circle.fill"
        case .provisional:
            return "clock.circle.fill"
        case .ephemeral:
            return "timer.circle.fill"
        @unknown default:
            return "questionmark.circle.fill"
        }
    }
    
    private var authorizationStatusText: String {
        switch pushNotificationService.authorizationStatus {
        case .authorized:
            return "מאושר"
        case .denied:
            return "נדחה"
        case .notDetermined:
            return "לא נקבע"
        case .provisional:
            return "זמני"
        case .ephemeral:
            return "ארעי"
        @unknown default:
            return "לא ידוע"
        }
    }
    
    private var authorizationStatusColor: Color {
        switch pushNotificationService.authorizationStatus {
        case .authorized:
            return Color.success
        case .denied:
            return Color.destructive
        case .notDetermined:
            return Color.warning
        case .provisional:
            return Color.cosmicAccent
        case .ephemeral:
            return Color.cosmicAccent
        @unknown default:
            return Color.mutedForeground
        }
    }
    
    // MARK: - Helper Methods
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    private func savePreferences() {
        guard let userId = authViewModel.currentUser?.id else { return }
        
        Task {
            await viewModel.saveNotificationPreferences(userId: userId)
        }
    }
}

// MARK: - Notification Settings ViewModel

@MainActor
class NotificationSettingsViewModel: ObservableObject {
    @Published var expenseApprovals = true
    @Published var invoiceReminders = true
    @Published var vatReminders = true
    @Published var paymentNotifications = true
    @Published var systemUpdates = true
    @Published var marketingNotifications = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadNotificationPreferences(userId: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let preferences = try await supabaseService.getNotificationPreferences(userId: userId)
            
            expenseApprovals = preferences.expenseApprovals
            invoiceReminders = preferences.invoiceReminders
            vatReminders = preferences.vatReminders
            paymentNotifications = preferences.paymentNotifications
            systemUpdates = preferences.systemUpdates
            marketingNotifications = preferences.marketingNotifications
            
        } catch {
            errorMessage = "שגיאה בטעינת העדפות: \(error.localizedDescription)"
            Logger.shared.error("Failed to load notification preferences: \(error)")
        }
        
        isLoading = false
    }
    
    func saveNotificationPreferences(userId: UUID) async {
        do {
            let preferences = NotificationPreferences(
                userId: userId,
                expenseApprovals: expenseApprovals,
                invoiceReminders: invoiceReminders,
                vatReminders: vatReminders,
                paymentNotifications: paymentNotifications,
                systemUpdates: systemUpdates,
                marketingNotifications: marketingNotifications
            )
            
            try await supabaseService.updateNotificationPreferences(preferences)
            Logger.shared.info("Notification preferences saved successfully")
            
        } catch {
            errorMessage = "שגיאה בשמירת העדפות: \(error.localizedDescription)"
            Logger.shared.error("Failed to save notification preferences: \(error)")
        }
    }
}

struct NotificationPreferences {
    let userId: UUID
    let expenseApprovals: Bool
    let invoiceReminders: Bool
    let vatReminders: Bool
    let paymentNotifications: Bool
    let systemUpdates: Bool
    let marketingNotifications: Bool
}

#Preview {
    NavigationView {
        NotificationSettingsView()
            .environmentObject(ThemeManager())
            .environmentObject(PushNotificationService.shared)
            .environmentObject(AuthViewModel())
    }
}
