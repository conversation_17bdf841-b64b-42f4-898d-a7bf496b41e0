//
//  SettingsView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var showingSignOutAlert = false
    @State private var showingDeleteAccountAlert = false
    @State private var showingAbout = false
    @State private var showingPrivacyPolicy = false
    @State private var showingTermsOfService = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                ScrollView {
                    LazyVStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                        // User Profile Section
                        if let user = authViewModel.currentUser,
                           let company = authViewModel.currentCompany {
                            ProfileSection(
                                user: user,
                                company: company,
                                isDark: themeManager.isDarkMode
                            )
                        }
                        
                        // App Settings
                        SettingsSection(title: "הגדרות אפליקציה") {
                            SettingsRow(
                                title: "מצב כהה",
                                icon: "moon.fill",
                                isDark: themeManager.isDarkMode
                            ) {
                                Toggle("", isOn: $themeManager.isDarkMode)
                                    .toggleStyle(SwitchToggleStyle(tint: Color.dynamicPrimary(themeManager.isDarkMode)))
                                    .onChange(of: themeManager.isDarkMode) { _ in
                                        themeManager.toggleTheme()
                                    }
                            }
                            
                            NavigationLink(destination: NotificationSettingsView()) {
                                SettingsRow(
                                    title: "התראות",
                                    icon: "bell.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                            
                            NavigationLink(destination: LanguageSettingsView()) {
                                SettingsRow(
                                    title: "שפה",
                                    icon: "globe",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    HStack {
                                        Text("עברית")
                                            .font(.hebrewFont(size: .base))
                                            .foregroundColor(Color.mutedForeground)
                                        
                                        Image(systemName: "chevron.left")
                                            .foregroundColor(Color.mutedForeground)
                                            .font(.caption)
                                    }
                                }
                            }
                        }
                        
                        // Business Settings
                        SettingsSection(title: "הגדרות עסק") {
                            NavigationLink(destination: CompanySettingsView()) {
                                SettingsRow(
                                    title: "פרטי החברה",
                                    icon: "building.2.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                            
                            NavigationLink(destination: SubscriptionView()) {
                                SettingsRow(
                                    title: "מנוי ותשלומים",
                                    icon: "creditcard.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    HStack {
                                        Text(authViewModel.currentCompany?.subscriptionTier.displayName ?? "")
                                            .font(.hebrewFont(size: .base))
                                            .foregroundColor(Color.mutedForeground)
                                        
                                        Image(systemName: "chevron.left")
                                            .foregroundColor(Color.mutedForeground)
                                            .font(.caption)
                                    }
                                }
                            }
                            
                            NavigationLink(destination: IntegrationsView()) {
                                SettingsRow(
                                    title: "אינטגרציות",
                                    icon: "link",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                        }
                        
                        // Support & Legal
                        SettingsSection(title: "תמיכה ומידע") {
                            NavigationLink(destination: HelpCenterView()) {
                                SettingsRow(
                                    title: "מרכז עזרה",
                                    icon: "questionmark.circle.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                            
                            Button(action: { showingPrivacyPolicy = true }) {
                                SettingsRow(
                                    title: "מדיניות פרטיות",
                                    icon: "hand.raised.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            Button(action: { showingTermsOfService = true }) {
                                SettingsRow(
                                    title: "תנאי שימוש",
                                    icon: "doc.text.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(Color.mutedForeground)
                                        .font(.caption)
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            Button(action: { showingAbout = true }) {
                                SettingsRow(
                                    title: "אודות האפליקציה",
                                    icon: "info.circle.fill",
                                    isDark: themeManager.isDarkMode
                                ) {
                                    HStack {
                                        Text("גרסה 1.0.0")
                                            .font(.hebrewFont(size: .base))
                                            .foregroundColor(Color.mutedForeground)
                                        
                                        Image(systemName: "chevron.left")
                                            .foregroundColor(Color.mutedForeground)
                                            .font(.caption)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // Account Actions
                        SettingsSection(title: "חשבון") {
                            Button(action: { showingSignOutAlert = true }) {
                                SettingsRow(
                                    title: "התנתק",
                                    icon: "rectangle.portrait.and.arrow.right.fill",
                                    isDark: themeManager.isDarkMode,
                                    isDestructive: false
                                ) {
                                    EmptyView()
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            Button(action: { showingDeleteAccountAlert = true }) {
                                SettingsRow(
                                    title: "מחק חשבון",
                                    icon: "trash.fill",
                                    isDark: themeManager.isDarkMode,
                                    isDestructive: true
                                ) {
                                    EmptyView()
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    .padding(.top, DesignSystem.Spacing.base.rawValue)
                }
            }
            .navigationTitle("הגדרות")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
        .alert("התנתקות", isPresented: $showingSignOutAlert) {
            Button("התנתק", role: .destructive) {
                Task {
                    await authViewModel.signOut()
                }
            }
            Button("ביטול", role: .cancel) { }
        } message: {
            Text("האם אתה בטוח שברצונך להתנתק?")
        }
        .alert("מחיקת חשבון", isPresented: $showingDeleteAccountAlert) {
            Button("מחק", role: .destructive) {
                // Implement account deletion
            }
            Button("ביטול", role: .cancel) { }
        } message: {
            Text("פעולה זו תמחק את החשבון שלך לצמיתות ולא ניתן לבטלה.")
        }
        .sheet(isPresented: $showingAbout) {
            AboutView()
        }
        .sheet(isPresented: $showingPrivacyPolicy) {
            WebView(url: "https://your-domain.com/privacy")
        }
        .sheet(isPresented: $showingTermsOfService) {
            WebView(url: "https://your-domain.com/terms")
        }
    }
}

struct ProfileSection: View {
    let user: User
    let company: Company
    let isDark: Bool
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            // Profile Header
            HStack(spacing: DesignSystem.Spacing.base.rawValue) {
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                    Text(user.fullName)
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                    
                    Text(user.email)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                    
                    Text(company.nameHebrew)
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicPrimary(isDark))
                }
                
                Spacer()
                
                // Profile Image Placeholder
                AsyncImage(url: company.logoUrl.flatMap(URL.init)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(Color.dynamicPrimary(isDark))
                }
                .frame(width: 80, height: 80)
                .background(Color.dynamicCard(isDark))
                .cornerRadius(DesignSystem.Radius.large.rawValue)
            }
        }
        .padding(DesignSystem.Spacing.lg.rawValue)
        .cardStyle(isDark: isDark)
    }
}

struct SettingsSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            Text(title)
                .font(.hebrewFont(size: .large, weight: .semibold))
                .foregroundColor(Color.mutedForeground)
            
            VStack(spacing: 0) {
                content
            }
        }
    }
}

struct SettingsRow<Accessory: View>: View {
    let title: String
    let icon: String
    let isDark: Bool
    let isDestructive: Bool
    let accessory: Accessory
    
    init(
        title: String,
        icon: String,
        isDark: Bool,
        isDestructive: Bool = false,
        @ViewBuilder accessory: () -> Accessory
    ) {
        self.title = title
        self.icon = icon
        self.isDark = isDark
        self.isDestructive = isDestructive
        self.accessory = accessory()
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.base.rawValue) {
            accessory
            
            Spacer()
            
            Text(title)
                .font(.hebrewFont(size: .base, weight: .medium))
                .foregroundColor(isDestructive ? Color.destructive : Color.dynamicForeground(isDark))
            
            Image(systemName: icon)
                .foregroundColor(isDestructive ? Color.destructive : Color.dynamicPrimary(isDark))
                .font(.title3)
                .frame(width: 24, height: 24)
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
}

// MARK: - Placeholder Views

struct NotificationSettingsView: View {
    var body: some View {
        VStack {
            Text("הגדרות התראות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("התראות")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct LanguageSettingsView: View {
    var body: some View {
        VStack {
            Text("הגדרות שפה")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("שפה")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct CompanySettingsView: View {
    var body: some View {
        VStack {
            Text("הגדרות חברה")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("פרטי החברה")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct SubscriptionView: View {
    var body: some View {
        VStack {
            Text("מנוי ותשלומים")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("מנוי")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct IntegrationsView: View {
    var body: some View {
        VStack {
            Text("אינטגרציות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("אינטגרציות")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct HelpCenterView: View {
    var body: some View {
        VStack {
            Text("מרכז עזרה")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .navigationTitle("עזרה")
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.primary)
                
                VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                    Text("מערכת חשבוניות")
                        .font(.hebrewFont(size: .xxxl, weight: .bold))
                    
                    Text("גרסה 1.0.0")
                        .font(.hebrewFont(size: .large))
                        .foregroundColor(.secondary)
                    
                    Text("מערכת ניהול חשבוניות והוצאות מתקדמת")
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
            }
            .padding(DesignSystem.Spacing.xl.rawValue)
            .navigationTitle("אודות")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("סגור") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct WebView: View {
    let url: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("תכונת דפדפן תהיה זמינה בקרוב")
                    .font(.hebrewFont(size: .base))
                    .foregroundColor(.secondary)
            }
            .navigationTitle("דף אינטרנט")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("סגור") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

#Preview {
    SettingsView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
