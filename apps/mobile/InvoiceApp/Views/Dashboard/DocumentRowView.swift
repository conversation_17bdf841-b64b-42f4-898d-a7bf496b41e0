//
//  DocumentRowView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct DocumentRowView: View {
    let document: Document
    let isDark: Bool
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.base.rawValue) {
            // Document actions
            VStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                Button(action: { shareDocument() }) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(Color.dynamicPrimary(isDark))
                        .font(.caption)
                }
                
                Button(action: { viewDocument() }) {
                    Image(systemName: "eye.fill")
                        .foregroundColor(Color.cosmicAccent)
                        .font(.caption)
                }
            }
            
            Spacer()
            
            // Document details
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                HStack {
                    // Status badge
                    Text(document.status.displayName)
                        .font(.hebrewFont(size: .xs, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, DesignSystem.Spacing.sm.rawValue)
                        .padding(.vertical, 2)
                        .background(document.status.color)
                        .cornerRadius(DesignSystem.Radius.small.rawValue)
                    
                    Spacer()
                    
                    // Document type and number
                    VStack(alignment: .trailing, spacing: 2) {
                        Text(document.documentType.displayName)
                            .font(.hebrewFont(size: .small, weight: .medium))
                            .foregroundColor(Color.dynamicForeground(isDark))
                        
                        Text(document.documentNumber)
                            .font(.hebrewFont(size: .xs))
                            .foregroundColor(Color.mutedForeground)
                    }
                }
                
                // Customer name
                if let customer = document.customer {
                    Text(customer.nameHebrew)
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicForeground(isDark))
                        .lineLimit(1)
                }
                
                HStack {
                    // Issue date
                    Text(formatDate(document.issueDate))
                        .font(.hebrewFont(size: .xs))
                        .foregroundColor(Color.mutedForeground)
                    
                    Spacer()
                    
                    // Amount
                    Text(formatCurrency(document.totalAmount))
                        .font(.hebrewFont(size: .base, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                }
            }
            
            // Document type icon
            Image(systemName: document.documentType.icon)
                .font(.title2)
                .foregroundColor(Color.dynamicPrimary(isDark))
                .frame(width: 40, height: 40)
                .background(Color.dynamicCard(isDark))
                .cornerRadius(DesignSystem.Radius.base.rawValue)
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.locale = Locale(identifier: "he_IL")
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
    
    private func shareDocument() {
        // Implement document sharing
        print("Share document: \(document.id)")
    }
    
    private func viewDocument() {
        // Implement document viewing
        print("View document: \(document.id)")
    }
}

#Preview {
    let sampleDocument = Document(
        id: UUID(),
        companyId: UUID(),
        documentType: .taxInvoice,
        documentNumber: "INV-2025-0001",
        customerId: UUID(),
        issueDate: Date(),
        dueDate: nil,
        currency: "ILS",
        subtotal: 1000.0,
        vatAmount: 180.0,
        totalAmount: 1180.0,
        status: .approved,
        itaAllocationNumber: "12345",
        itaAllocationDate: Date(),
        notes: nil,
        pdfUrl: nil,
        sentAt: nil,
        createdAt: Date(),
        updatedAt: Date(),
        customer: Customer(
            id: UUID(),
            companyId: UUID(),
            businessNumber: "*********",
            nameHebrew: "חברת הדוגמה בע\"מ",
            nameEnglish: "Example Company Ltd",
            vatId: "*********",
            billingAddressHebrew: "רחוב הדוגמה 123",
            billingAddressEnglish: nil,
            shippingAddressHebrew: nil,
            shippingAddressEnglish: nil,
            cityHebrew: "תל אביב",
            cityEnglish: nil,
            contactName: "יוחנן כהן",
            contactEmail: "<EMAIL>",
            contactPhone: "03-1234567",
            notes: nil,
            createdAt: Date(),
            updatedAt: Date()
        ),
        items: nil
    )
    
    DocumentRowView(document: sampleDocument, isDark: true)
        .padding()
        .background(Color.backgroundPrimary)
}
