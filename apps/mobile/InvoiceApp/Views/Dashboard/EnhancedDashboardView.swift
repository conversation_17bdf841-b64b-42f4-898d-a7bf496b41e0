//
//  EnhancedDashboardView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct EnhancedDashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingNotifications = false
    @State private var showingCreateDocument = false
    @State private var showingExpenseScanner = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                if viewModel.isLoading {
                    LoadingView(
                        message: "טוען נתונים...",
                        isDark: themeManager.isDarkMode
                    )
                } else {
                    ScrollView {
                        LazyVStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                            // Header with company info
                            headerSection
                            
                            // Key Metrics Cards
                            metricsSection
                            
                            // Quick Actions
                            quickActionsSection
                            
                            // Recent Activity
                            recentActivitySection
                            
                            // Insights and Alerts
                            insightsSection
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        .padding(.top, DesignSystem.Spacing.base.rawValue)
                    }
                    .refreshable {
                        await viewModel.refreshData()
                        HapticManager.shared.impact(.light)
                    }
                }
                
                // Floating Action Button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        EnhancedFloatingActionButton(
                            isDark: themeManager.isDarkMode,
                            onCreateDocument: { showingCreateDocument = true },
                            onScanExpense: { showingExpenseScanner = true }
                        )
                    }
                }
                .padding(.trailing, DesignSystem.Spacing.lg.rawValue)
                .padding(.bottom, DesignSystem.Spacing.massive.rawValue)
            }
            .navigationTitle("לוח בקרה")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NotificationButton(
                        count: viewModel.pendingExpensesCount,
                        isDark: themeManager.isDarkMode
                    ) {
                        showingNotifications = true
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    ProfileButton(
                        user: authViewModel.currentUser,
                        isDark: themeManager.isDarkMode
                    )
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $showingNotifications) {
            NotificationsView()
        }
        .sheet(isPresented: $showingCreateDocument) {
            CreateDocumentView()
        }
        .sheet(isPresented: $showingExpenseScanner) {
            ExpenseScannerView()
        }
        .onAppear {
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadDashboardData(companyId: companyId)
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // Refresh data when app comes to foreground
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadDashboardData(companyId: companyId)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: DesignSystem.Spacing.base.rawValue) {
            if let company = authViewModel.currentCompany,
               let user = authViewModel.currentUser {
                HStack(spacing: DesignSystem.Spacing.base.rawValue) {
                    VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                        Text("שלום, \(user.fullName)")
                            .font(.hebrewFont(size: .xl, weight: .semibold))
                            .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                        
                        Text(company.nameHebrew)
                            .font(.hebrewFont(size: .base, weight: .medium))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                        
                        HStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                            StatusBadge(
                                text: company.subscriptionTier.displayName,
                                color: company.subscriptionTier == .paid ? Color.success : Color.cosmicAccent,
                                size: .small
                            )
                            
                            Text("•")
                                .foregroundColor(Color.mutedForeground)
                            
                            Text(formatDate(Date()))
                                .font(.hebrewFont(size: .small))
                                .foregroundColor(Color.mutedForeground)
                        }
                    }
                    
                    Spacer()
                    
                    // Company Logo or Avatar
                    AsyncImage(url: company.logoUrl.flatMap(URL.init)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        Image(systemName: "building.2.fill")
                            .font(.system(size: 24))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    }
                    .frame(width: 60, height: 60)
                    .background(Color.dynamicCard(themeManager.isDarkMode))
                    .cornerRadius(DesignSystem.Radius.large.rawValue)
                    .cosmicGlow(isDark: themeManager.isDarkMode)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg.rawValue)
        .cardStyle(isDark: themeManager.isDarkMode)
    }
    
    // MARK: - Metrics Section
    
    private var metricsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "מדדים עיקריים",
                subtitle: "החודש הנוכחי",
                isDark: themeManager.isDarkMode
            )
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: DesignSystem.Spacing.base.rawValue) {
                MetricCard(
                    title: "הכנסות החודש",
                    value: viewModel.formatCurrency(viewModel.monthlyRevenue),
                    change: "+12%",
                    changeType: .positive,
                    icon: "chart.line.uptrend.xyaxis",
                    isDark: themeManager.isDarkMode
                )
                
                MetricCard(
                    title: "מע״מ לתשלום",
                    value: viewModel.formatCurrency(viewModel.vatLiability),
                    change: viewModel.nextPaymentDateText,
                    changeType: .neutral,
                    icon: "creditcard.fill",
                    isDark: themeManager.isDarkMode
                )
                
                MetricCard(
                    title: "חשבוניות פתוחות",
                    value: "\(viewModel.openInvoicesCount)",
                    change: viewModel.formatCurrency(viewModel.openInvoicesAmount),
                    changeType: .neutral,
                    icon: "doc.text.fill",
                    isDark: themeManager.isDarkMode
                )
                
                MetricCard(
                    title: "הוצאות ממתינות",
                    value: "\(viewModel.pendingExpensesCount)",
                    change: "לבדיקה",
                    changeType: viewModel.pendingExpensesCount > 0 ? .negative : .positive,
                    icon: "exclamationmark.circle.fill",
                    isDark: themeManager.isDarkMode
                )
            }
        }
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "פעולות מהירות",
                isDark: themeManager.isDarkMode
            )
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: DesignSystem.Spacing.base.rawValue) {
                QuickActionCard(
                    title: "חשבונית חדשה",
                    icon: "doc.text.fill",
                    color: Color.dynamicPrimary(themeManager.isDarkMode),
                    isDark: themeManager.isDarkMode
                ) {
                    showingCreateDocument = true
                    HapticManager.shared.selection()
                }
                
                QuickActionCard(
                    title: "סרוק הוצאה",
                    icon: "camera.fill",
                    color: Color.cosmicAccent,
                    isDark: themeManager.isDarkMode
                ) {
                    showingExpenseScanner = true
                    HapticManager.shared.selection()
                }
                
                QuickActionCard(
                    title: "לקוח חדש",
                    icon: "person.badge.plus.fill",
                    color: Color.success,
                    isDark: themeManager.isDarkMode
                ) {
                    // Navigate to create customer
                    HapticManager.shared.selection()
                }
            }
        }
    }
    
    // MARK: - Recent Activity Section
    
    private var recentActivitySection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "פעילות אחרונה",
                actionTitle: "הצג הכל",
                isDark: themeManager.isDarkMode
            ) {
                // Navigate to activity log
            }
            
            if viewModel.recentDocuments.isEmpty {
                EmptyStateView(
                    icon: "clock.arrow.circlepath",
                    title: "אין פעילות אחרונה",
                    subtitle: "הפעילות האחרונה שלך תופיע כאן",
                    actionTitle: nil,
                    action: nil,
                    isDark: themeManager.isDarkMode
                )
                .frame(height: 150)
            } else {
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    ForEach(viewModel.recentDocuments.prefix(3)) { document in
                        ActivityRowView(
                            document: document,
                            isDark: themeManager.isDarkMode
                        )
                    }
                }
            }
        }
    }
    
    // MARK: - Insights Section
    
    private var insightsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            SectionHeader(
                title: "תובנות והתראות",
                isDark: themeManager.isDarkMode
            )
            
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                if viewModel.pendingExpensesCount > 0 {
                    InsightCard(
                        type: .warning,
                        title: "הוצאות ממתינות לאישור",
                        message: "יש לך \(viewModel.pendingExpensesCount) הוצאות שממתינות לאישור",
                        actionTitle: "בדוק עכשיו",
                        isDark: themeManager.isDarkMode
                    ) {
                        // Navigate to expenses
                    }
                }
                
                if viewModel.vatLiability > 0 {
                    InsightCard(
                        type: .info,
                        title: "תזכורת מע״מ",
                        message: "מועד תשלום המע״מ הבא: \(viewModel.nextPaymentDateText)",
                        actionTitle: "הצג דוח",
                        isDark: themeManager.isDarkMode
                    ) {
                        // Navigate to VAT report
                    }
                }
                
                InsightCard(
                    type: .success,
                    title: "ביצועים טובים",
                    message: "ההכנסות שלך עלו ב-12% לעומת החודש הקודם",
                    actionTitle: nil,
                    isDark: themeManager.isDarkMode
                ) {
                    // No action
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let title: String
    let value: String
    let change: String
    let changeType: ChangeType
    let icon: String
    let isDark: Bool
    
    enum ChangeType {
        case positive, negative, neutral
        
        var color: Color {
            switch self {
            case .positive: return Color.success
            case .negative: return Color.destructive
            case .neutral: return Color.mutedForeground
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(Color.dynamicPrimary(isDark))
                    .font(.title2)
                
                Spacer()
                
                Text(title)
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(value)
                    .font(.hebrewFont(size: .xl, weight: .bold))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                Text(change)
                    .font(.hebrewFont(size: .xs))
                    .foregroundColor(changeType.color)
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
        .cosmicGlow(isDark: isDark)
    }
}

struct QuickActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let isDark: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.dynamicForeground(isDark))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .cardStyle(isDark: isDark)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ActivityRowView: View {
    let document: Document
    let isDark: Bool
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.base.rawValue) {
            VStack(alignment: .trailing, spacing: 2) {
                Text(formatTime(document.createdAt))
                    .font(.hebrewFont(size: .xs))
                    .foregroundColor(Color.mutedForeground)
                
                StatusBadge(
                    text: document.status.displayName,
                    color: document.status.color,
                    size: .small
                )
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(document.documentType.displayName) #\(document.documentNumber)")
                    .font(.hebrewFont(size: .base, weight: .medium))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                if let customer = document.customer {
                    Text(customer.nameHebrew)
                        .font(.hebrewFont(size: .small))
                        .foregroundColor(Color.mutedForeground)
                }
            }
            
            Image(systemName: document.documentType.icon)
                .font(.title3)
                .foregroundColor(Color.dynamicPrimary(isDark))
                .frame(width: 32, height: 32)
                .background(Color.dynamicCard(isDark))
                .cornerRadius(DesignSystem.Radius.small.rawValue)
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

struct InsightCard: View {
    let type: InsightType
    let title: String
    let message: String
    let actionTitle: String?
    let isDark: Bool
    let action: (() -> Void)?
    
    enum InsightType {
        case info, warning, success, error
        
        var color: Color {
            switch self {
            case .info: return Color.cosmicAccent
            case .warning: return Color.warning
            case .success: return Color.success
            case .error: return Color.destructive
            }
        }
        
        var icon: String {
            switch self {
            case .info: return "info.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .success: return "checkmark.circle.fill"
            case .error: return "xmark.circle.fill"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.base.rawValue) {
            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                        .font(.hebrewFont(size: .small, weight: .medium))
                        .foregroundColor(type.color)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                Text(title)
                    .font(.hebrewFont(size: .base, weight: .semibold))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                Text(message)
                    .font(.hebrewFont(size: .small))
                    .foregroundColor(Color.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            Image(systemName: type.icon)
                .font(.title2)
                .foregroundColor(type.color)
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
}

struct NotificationButton: View {
    let count: Int
    let isDark: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                Image(systemName: "bell.fill")
                    .foregroundColor(Color.dynamicPrimary(isDark))
                    .font(.title2)
                
                if count > 0 {
                    VStack {
                        HStack {
                            Spacer()
                            Text("\(count)")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.destructive)
                                .clipShape(Circle())
                        }
                        Spacer()
                    }
                }
            }
        }
    }
}

struct ProfileButton: View {
    let user: User?
    let isDark: Bool
    
    var body: some View {
        Button(action: {
            // Navigate to profile
        }) {
            HStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                Text(user?.fullName.prefix(1).uppercased() ?? "U")
                    .font(.hebrewFont(size: .base, weight: .semibold))
                    .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
                    .frame(width: 32, height: 32)
                    .background(Color.dynamicPrimary(isDark))
                    .clipShape(Circle())
            }
        }
    }
}

struct EnhancedFloatingActionButton: View {
    let isDark: Bool
    let onCreateDocument: () -> Void
    let onScanExpense: () -> Void
    
    @State private var showingActionSheet = false
    
    var body: some View {
        Button(action: { showingActionSheet = true }) {
            Image(systemName: "plus")
                .font(.title2)
                .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
                .frame(width: 56, height: 56)
                .background(Color.dynamicPrimary(isDark))
                .clipShape(Circle())
                .cosmicGlow(isDark: isDark)
        }
        .confirmationDialog("פעולה חדשה", isPresented: $showingActionSheet) {
            Button("חשבונית חדשה") {
                onCreateDocument()
                HapticManager.shared.selection()
            }
            Button("סרוק הוצאה") {
                onScanExpense()
                HapticManager.shared.selection()
            }
            Button("לקוח חדש") {
                // Navigate to create customer
                HapticManager.shared.selection()
            }
        }
    }
}

struct NotificationsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("התראות")
                    .font(.hebrewFont(size: .xl, weight: .semibold))
                Text("תכונה זו תהיה זמינה בקרוב")
                    .font(.hebrewFont(size: .base))
                    .foregroundColor(.secondary)
            }
            .navigationTitle("התראות")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("סגור") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

#Preview {
    EnhancedDashboardView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
