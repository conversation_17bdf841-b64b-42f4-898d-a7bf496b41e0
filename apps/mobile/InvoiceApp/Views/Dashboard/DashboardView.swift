//
//  DashboardView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                ScrollView {
                    LazyVStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                        // Header with company info
                        headerSection
                        
                        // Stats Cards
                        statsCardsSection
                        
                        // Quick Actions
                        quickActionsSection
                        
                        // Recent Documents
                        recentDocumentsSection
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    .padding(.top, DesignSystem.Spacing.base.rawValue)
                }
                .refreshable {
                    await viewModel.refreshData()
                }
                
                // Floating Action Button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        FloatingActionButton(isDark: themeManager.isDarkMode)
                    }
                }
                .padding(.trailing, DesignSystem.Spacing.lg.rawValue)
                .padding(.bottom, DesignSystem.Spacing.massive.rawValue)
            }
            .navigationTitle("לוח בקרה")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        // Show notifications
                    }) {
                        ZStack {
                            Image(systemName: "bell.fill")
                                .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            
                            if viewModel.pendingExpensesCount > 0 {
                                VStack {
                                    HStack {
                                        Spacer()
                                        Text("\(viewModel.pendingExpensesCount)")
                                            .font(.caption2)
                                            .foregroundColor(.white)
                                            .padding(4)
                                            .background(Color.destructive)
                                            .clipShape(Circle())
                                    }
                                    Spacer()
                                }
                            }
                        }
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadDashboardData(companyId: companyId)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: DesignSystem.Spacing.base.rawValue) {
            if let company = authViewModel.currentCompany {
                HStack {
                    VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                        Text("שלום, \(authViewModel.currentUser?.fullName ?? "")")
                            .font(.hebrewFont(size: .large, weight: .medium))
                            .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                        
                        Text(company.nameHebrew)
                            .font(.hebrewFont(size: .base))
                            .foregroundColor(Color.mutedForeground)
                    }
                    
                    Spacer()
                    
                    // Company Logo or Placeholder
                    AsyncImage(url: company.logoUrl.flatMap(URL.init)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        Image(systemName: "building.2.fill")
                            .font(.system(size: 24))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    }
                    .frame(width: 50, height: 50)
                    .background(Color.dynamicCard(themeManager.isDarkMode))
                    .cornerRadius(DesignSystem.Radius.base.rawValue)
                }
            }
        }
    }
    
    // MARK: - Stats Cards Section
    
    private var statsCardsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: DesignSystem.Spacing.base.rawValue) {
            StatCard(
                title: "הכנסות החודש",
                value: viewModel.formatCurrency(viewModel.monthlyRevenue),
                subtitle: viewModel.revenueChangeText,
                icon: "chart.line.uptrend.xyaxis",
                color: Color.success,
                isDark: themeManager.isDarkMode
            )
            
            StatCard(
                title: "מע״מ לתשלום",
                value: viewModel.formatCurrency(viewModel.vatLiability),
                subtitle: viewModel.nextPaymentDateText,
                icon: "creditcard.fill",
                color: Color.warning,
                isDark: themeManager.isDarkMode
            )
            
            StatCard(
                title: "חשבוניות פתוחות",
                value: "\(viewModel.openInvoicesCount)",
                subtitle: viewModel.formatCurrency(viewModel.openInvoicesAmount),
                icon: "doc.text.fill",
                color: Color.cosmicAccent,
                isDark: themeManager.isDarkMode
            )
            
            StatCard(
                title: "הוצאות ממתינות",
                value: "\(viewModel.pendingExpensesCount)",
                subtitle: "לבדיקה",
                icon: "exclamationmark.circle.fill",
                color: viewModel.pendingExpensesCount > 0 ? Color.destructive : Color.success,
                isDark: themeManager.isDarkMode
            )
        }
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            Text("פעולות מהירות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
            
            HStack(spacing: DesignSystem.Spacing.base.rawValue) {
                QuickActionButton(
                    title: "חשבונית חדשה",
                    icon: "doc.text.fill",
                    color: Color.dynamicPrimary(themeManager.isDarkMode),
                    isDark: themeManager.isDarkMode
                ) {
                    // Navigate to create invoice
                }
                
                QuickActionButton(
                    title: "סרוק הוצאה",
                    icon: "camera.fill",
                    color: Color.cosmicAccent,
                    isDark: themeManager.isDarkMode
                ) {
                    // Navigate to scan expense
                }
            }
        }
    }
    
    // MARK: - Recent Documents Section
    
    private var recentDocumentsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
            HStack {
                NavigationLink(destination: DocumentsView()) {
                    Text("הצג הכל")
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                }
                
                Spacer()
                
                Text("מסמכים אחרונים")
                    .font(.hebrewFont(size: .xl, weight: .semibold))
                    .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
            }
            
            if viewModel.recentDocuments.isEmpty {
                VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 40))
                        .foregroundColor(Color.mutedForeground)
                    
                    Text("אין מסמכים עדיין")
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                    
                    Text("צור את החשבונית הראשונה שלך")
                        .font(.hebrewFont(size: .small))
                        .foregroundColor(Color.mutedForeground)
                }
                .frame(maxWidth: .infinity)
                .padding(DesignSystem.Spacing.xl.rawValue)
                .cardStyle(isDark: themeManager.isDarkMode)
            } else {
                LazyVStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    ForEach(viewModel.recentDocuments) { document in
                        DocumentRowView(document: document, isDark: themeManager.isDarkMode)
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    let isDark: Bool
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Spacer()
                
                Text(title)
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(value)
                    .font(.hebrewFont(size: .xl, weight: .bold))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                Text(subtitle)
                    .font(.hebrewFont(size: .xs))
                    .foregroundColor(Color.mutedForeground)
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let isDark: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.dynamicForeground(isDark))
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(DesignSystem.Spacing.base.rawValue)
            .cardStyle(isDark: isDark)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FloatingActionButton: View {
    let isDark: Bool
    @State private var showingActionSheet = false
    
    var body: some View {
        Button(action: { showingActionSheet = true }) {
            Image(systemName: "plus")
                .font(.title2)
                .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
                .frame(width: 56, height: 56)
                .background(Color.dynamicPrimary(isDark))
                .clipShape(Circle())
                .cosmicGlow(isDark: isDark)
        }
        .confirmationDialog("פעולה חדשה", isPresented: $showingActionSheet) {
            Button("חשבונית חדשה") {
                // Navigate to create invoice
            }
            Button("קבלה חדשה") {
                // Navigate to create receipt
            }
            Button("לקוח חדש") {
                // Navigate to create customer
            }
            Button("סרוק הוצאה") {
                // Navigate to scan expense
            }
        }
    }
}

#Preview {
    DashboardView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
