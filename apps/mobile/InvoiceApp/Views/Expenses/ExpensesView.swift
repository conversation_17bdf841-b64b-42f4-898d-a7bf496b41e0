//
//  ExpensesView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct ExpensesView: View {
    @StateObject private var viewModel = ExpensesViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var selectedFilter: ExpenseFilter = .all
    @State private var showingScanner = false
    
    enum ExpenseFilter: String, CaseIterable {
        case all = "הכל"
        case pending = "ממתינות"
        case approved = "אושרו"
        case rejected = "נדחו"
        case duplicates = "כפולות"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Filter Tabs
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                            ForEach(ExpenseFilter.allCases, id: \.self) { filter in
                                FilterTab(
                                    title: filter.rawValue,
                                    isSelected: selectedFilter == filter,
                                    isDark: themeManager.isDarkMode,
                                    badgeCount: viewModel.getFilterCount(filter)
                                ) {
                                    selectedFilter = filter
                                    Task {
                                        await viewModel.loadExpenses(filter: filter)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    }
                    .padding(.vertical, DesignSystem.Spacing.base.rawValue)
                    
                    // Expenses List
                    if viewModel.isLoading {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: Color.dynamicPrimary(themeManager.isDarkMode)))
                        Spacer()
                    } else if viewModel.expenses.isEmpty {
                        EmptyExpensesView(
                            filter: selectedFilter,
                            isDark: themeManager.isDarkMode,
                            onScanExpense: { showingScanner = true }
                        )
                    } else {
                        ScrollView {
                            LazyVStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                                ForEach(viewModel.expenses) { expense in
                                    ExpenseRowView(
                                        expense: expense,
                                        isDark: themeManager.isDarkMode,
                                        onApprove: { await viewModel.approveExpense(expense.id) },
                                        onReject: { reason in await viewModel.rejectExpense(expense.id, reason: reason) }
                                    )
                                }
                            }
                            .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        }
                        .refreshable {
                            await viewModel.refreshExpenses()
                        }
                    }
                }
            }
            .navigationTitle("הוצאות")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingScanner = true }) {
                        Image(systemName: "camera.fill")
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $showingScanner) {
            ExpenseScannerView()
        }
        .onAppear {
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadExpenses(companyId: companyId, filter: selectedFilter)
                }
            }
        }
    }
}

struct FilterTab: View {
    let title: String
    let isSelected: Bool
    let isDark: Bool
    let badgeCount: Int?
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                if let count = badgeCount, count > 0 {
                    Text("\(count)")
                        .font(.hebrewFont(size: .xs, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.destructive)
                        .clipShape(Capsule())
                }
                
                Text(title)
                    .font(.hebrewFont(size: .base, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? (isDark ? Color.backgroundPrimary : Color.foregroundLight) : Color.mutedForeground)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
            .padding(.vertical, DesignSystem.Spacing.sm.rawValue)
            .background(
                isSelected ? Color.dynamicPrimary(isDark) : Color.clear
            )
            .cornerRadius(DesignSystem.Radius.full.rawValue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EmptyExpensesView: View {
    let filter: ExpensesView.ExpenseFilter
    let isDark: Bool
    let onScanExpense: () -> Void
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            Spacer()
            
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: "camera.fill")
                    .font(.system(size: 60))
                    .foregroundColor(Color.mutedForeground)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text(emptyStateTitle)
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                        .multilineTextAlignment(.center)
                    
                    Text(emptyStateSubtitle)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
                
                if filter == .all || filter == .pending {
                    Button(action: onScanExpense) {
                        HStack {
                            Image(systemName: "camera.fill")
                            Text("סרוק הוצאה")
                        }
                        .font(.hebrewFont(size: .large, weight: .semibold))
                        .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(Color.dynamicPrimary(isDark))
                        .cornerRadius(DesignSystem.Radius.base.rawValue)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.massive.rawValue)
                }
            }
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
    
    private var emptyStateTitle: String {
        switch filter {
        case .all:
            return "אין הוצאות עדיין"
        case .pending:
            return "אין הוצאות ממתינות"
        case .approved:
            return "אין הוצאות מאושרות"
        case .rejected:
            return "אין הוצאות שנדחו"
        case .duplicates:
            return "אין הוצאות כפולות"
        }
    }
    
    private var emptyStateSubtitle: String {
        switch filter {
        case .all:
            return "סרוק קבלות והוצאות כדי להתחיל"
        case .pending:
            return "הוצאות שממתינות לאישור יופיעו כאן"
        case .approved:
            return "הוצאות מאושרות יופיעו כאן"
        case .rejected:
            return "הוצאות שנדחו יופיעו כאן"
        case .duplicates:
            return "הוצאות שזוהו ככפולות יופיעו כאן"
        }
    }
}

struct ExpenseRowView: View {
    let expense: Expense
    let isDark: Bool
    let onApprove: () async -> Void
    let onReject: (String) async -> Void
    
    @State private var showingRejectDialog = false
    @State private var rejectionReason = ""
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.base.rawValue) {
            HStack(spacing: DesignSystem.Spacing.base.rawValue) {
                // Actions for pending expenses
                if expense.status == .pending {
                    VStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                        Button(action: { Task { await onApprove() } }) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(Color.success)
                                .font(.title2)
                        }
                        
                        Button(action: { showingRejectDialog = true }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(Color.destructive)
                                .font(.title2)
                        }
                    }
                }
                
                Spacer()
                
                // Expense details
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
                    HStack {
                        // Status and duplicate warning
                        HStack(spacing: DesignSystem.Spacing.xs.rawValue) {
                            if expense.duplicateRisk != .none {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(Color.warning)
                                    .font(.caption)
                            }
                            
                            Text(expense.status.displayName)
                                .font(.hebrewFont(size: .xs, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, DesignSystem.Spacing.sm.rawValue)
                                .padding(.vertical, 2)
                                .background(expense.status.color)
                                .cornerRadius(DesignSystem.Radius.small.rawValue)
                        }
                        
                        Spacer()
                        
                        // Vendor name
                        Text(expense.vendorName)
                            .font(.hebrewFont(size: .base, weight: .medium))
                            .foregroundColor(Color.dynamicForeground(isDark))
                            .lineLimit(1)
                    }
                    
                    // Category
                    Text(expense.category.displayName)
                        .font(.hebrewFont(size: .small))
                        .foregroundColor(Color.mutedForeground)
                    
                    HStack {
                        // Date
                        Text(formatDate(expense.expenseDate))
                            .font(.hebrewFont(size: .xs))
                            .foregroundColor(Color.mutedForeground)
                        
                        Spacer()
                        
                        // Amount
                        Text(formatCurrency(expense.totalAmount))
                            .font(.hebrewFont(size: .base, weight: .semibold))
                            .foregroundColor(Color.dynamicForeground(isDark))
                    }
                }
                
                // Source icon
                Image(systemName: sourceIcon)
                    .font(.title2)
                    .foregroundColor(Color.dynamicPrimary(isDark))
                    .frame(width: 40, height: 40)
                    .background(Color.dynamicCard(isDark))
                    .cornerRadius(DesignSystem.Radius.base.rawValue)
            }
            
            // Duplicate warning
            if expense.duplicateRisk == .high {
                HStack {
                    Spacer()
                    
                    Text("ייתכן שזו הוצאה כפולה")
                        .font(.hebrewFont(size: .small, weight: .medium))
                        .foregroundColor(Color.warning)
                        .padding(.horizontal, DesignSystem.Spacing.sm.rawValue)
                        .padding(.vertical, 4)
                        .background(Color.warning.opacity(0.1))
                        .cornerRadius(DesignSystem.Radius.small.rawValue)
                }
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
        .alert("דחיית הוצאה", isPresented: $showingRejectDialog) {
            TextField("סיבת הדחייה", text: $rejectionReason)
            Button("דחה") {
                Task {
                    await onReject(rejectionReason)
                    rejectionReason = ""
                }
            }
            Button("ביטול", role: .cancel) {
                rejectionReason = ""
            }
        } message: {
            Text("אנא הזן סיבה לדחיית ההוצאה")
        }
    }
    
    private var sourceIcon: String {
        switch expense.source {
        case .manual:
            return "hand.raised.fill"
        case .emailScan:
            return "envelope.fill"
        case .upload:
            return "arrow.up.doc.fill"
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.locale = Locale(identifier: "he_IL")
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
}

// MARK: - Expenses ViewModel

@MainActor
class ExpensesViewModel: ObservableObject {
    @Published var expenses: [Expense] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadExpenses(companyId: UUID? = nil, filter: ExpensesView.ExpenseFilter) async {
        guard let companyId = companyId else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let status: ExpenseStatus? = {
                switch filter {
                case .pending: return .pending
                case .approved: return .approved
                case .rejected: return .rejected
                default: return nil
                }
            }()
            
            let allExpenses = try await supabaseService.getExpenses(companyId: companyId, status: status)
            expenses = filterExpenses(allExpenses, by: filter)
        } catch {
            errorMessage = "שגיאה בטעינת הוצאות: \(error.localizedDescription)"
            print("Expenses loading error: \(error)")
        }
        
        isLoading = false
    }
    
    func refreshExpenses() async {
        // Implement refresh logic
    }
    
    func approveExpense(_ expenseId: UUID) async {
        do {
            _ = try await supabaseService.updateExpenseStatus(expenseId: expenseId, status: .approved)
            // Update local state
            if let index = expenses.firstIndex(where: { $0.id == expenseId }) {
                expenses[index] = expenses[index] // Would update with new status
            }
        } catch {
            errorMessage = "שגיאה באישור הוצאה: \(error.localizedDescription)"
        }
    }
    
    func rejectExpense(_ expenseId: UUID, reason: String) async {
        do {
            _ = try await supabaseService.updateExpenseStatus(expenseId: expenseId, status: .rejected, rejectionReason: reason)
            // Update local state
            if let index = expenses.firstIndex(where: { $0.id == expenseId }) {
                expenses[index] = expenses[index] // Would update with new status
            }
        } catch {
            errorMessage = "שגיאה בדחיית הוצאה: \(error.localizedDescription)"
        }
    }
    
    func getFilterCount(_ filter: ExpensesView.ExpenseFilter) -> Int? {
        switch filter {
        case .pending:
            return expenses.filter { $0.status == .pending }.count
        case .duplicates:
            return expenses.filter { $0.duplicateRisk != .none }.count
        default:
            return nil
        }
    }
    
    private func filterExpenses(_ expenses: [Expense], by filter: ExpensesView.ExpenseFilter) -> [Expense] {
        switch filter {
        case .all:
            return expenses
        case .pending:
            return expenses.filter { $0.status == .pending }
        case .approved:
            return expenses.filter { $0.status == .approved }
        case .rejected:
            return expenses.filter { $0.status == .rejected }
        case .duplicates:
            return expenses.filter { $0.duplicateRisk != .none }
        }
    }
}

struct ExpenseScannerView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("סורק הוצאות")
                    .font(.hebrewFont(size: .xl, weight: .semibold))
                
                Text("תכונה זו תהיה זמינה בקרוב")
                    .font(.hebrewFont(size: .base))
                    .foregroundColor(.secondary)
            }
            .navigationTitle("סרוק הוצאה")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

#Preview {
    ExpensesView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
