//
//  ReportsView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct ReportsView: View {
    @StateObject private var viewModel = ReportsViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var selectedPeriod: ReportPeriod = .currentMonth
    @State private var selectedTab: ReportTab = .vat
    
    enum ReportTab: String, CaseIterable {
        case vat = "מע״מ"
        case income = "הכנסות"
        case expenses = "הוצאות"
        case customers = "לקוחות"
    }
    
    enum ReportPeriod: String, CaseIterable {
        case currentMonth = "החודש הנוכחי"
        case lastMonth = "החודש הקודם"
        case currentQuarter = "הרבעון הנוכחי"
        case currentYear = "השנה הנוכחית"
        case custom = "תקופה מותאמת"
        
        var dateRange: (start: Date, end: Date) {
            let calendar = Calendar.current
            let now = Date()
            
            switch self {
            case .currentMonth:
                let start = calendar.dateInterval(of: .month, for: now)?.start ?? now
                let end = calendar.dateInterval(of: .month, for: now)?.end ?? now
                return (start, end)
            case .lastMonth:
                let lastMonth = calendar.date(byAdding: .month, value: -1, to: now) ?? now
                let start = calendar.dateInterval(of: .month, for: lastMonth)?.start ?? now
                let end = calendar.dateInterval(of: .month, for: lastMonth)?.end ?? now
                return (start, end)
            case .currentQuarter:
                let quarter = calendar.component(.quarter, from: now)
                let year = calendar.component(.year, from: now)
                let startMonth = (quarter - 1) * 3 + 1
                let start = calendar.date(from: DateComponents(year: year, month: startMonth, day: 1)) ?? now
                let end = calendar.date(byAdding: DateComponents(month: 3, day: -1), to: start) ?? now
                return (start, end)
            case .currentYear:
                let start = calendar.dateInterval(of: .year, for: now)?.start ?? now
                let end = calendar.dateInterval(of: .year, for: now)?.end ?? now
                return (start, end)
            case .custom:
                return (now, now) // Would be set by date picker
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.dynamicBackground(themeManager.isDarkMode)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Period Selector
                    VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                        HStack {
                            Menu {
                                ForEach(ReportPeriod.allCases, id: \.self) { period in
                                    Button(period.rawValue) {
                                        selectedPeriod = period
                                        Task {
                                            await viewModel.loadReports(period: period)
                                        }
                                    }
                                }
                            } label: {
                                HStack {
                                    Image(systemName: "chevron.down")
                                        .foregroundColor(Color.mutedForeground)
                                    
                                    Spacer()
                                    
                                    Text(selectedPeriod.rawValue)
                                        .font(.hebrewFont(size: .base, weight: .medium))
                                        .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                                }
                                .padding(DesignSystem.Spacing.base.rawValue)
                                .cardStyle(isDark: themeManager.isDarkMode)
                            }
                            
                            Button(action: { Task { await viewModel.exportReports() } }) {
                                Image(systemName: "square.and.arrow.up")
                                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                                    .font(.title2)
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        
                        // Report Tabs
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                                ForEach(ReportTab.allCases, id: \.self) { tab in
                                    ReportTabButton(
                                        title: tab.rawValue,
                                        isSelected: selectedTab == tab,
                                        isDark: themeManager.isDarkMode
                                    ) {
                                        selectedTab = tab
                                    }
                                }
                            }
                            .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                        }
                    }
                    .padding(.vertical, DesignSystem.Spacing.base.rawValue)
                    
                    // Report Content
                    ScrollView {
                        LazyVStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                            switch selectedTab {
                            case .vat:
                                VATReportView(viewModel: viewModel, isDark: themeManager.isDarkMode)
                            case .income:
                                IncomeReportView(viewModel: viewModel, isDark: themeManager.isDarkMode)
                            case .expenses:
                                ExpensesReportView(viewModel: viewModel, isDark: themeManager.isDarkMode)
                            case .customers:
                                CustomersReportView(viewModel: viewModel, isDark: themeManager.isDarkMode)
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                    }
                    .refreshable {
                        await viewModel.refreshReports()
                    }
                }
            }
            .navigationTitle("דוחות")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            if let companyId = authViewModel.currentCompany?.id {
                Task {
                    await viewModel.loadReports(companyId: companyId, period: selectedPeriod)
                }
            }
        }
    }
}

struct ReportTabButton: View {
    let title: String
    let isSelected: Bool
    let isDark: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.hebrewFont(size: .base, weight: isSelected ? .semibold : .medium))
                .foregroundColor(isSelected ? (isDark ? Color.backgroundPrimary : Color.foregroundLight) : Color.mutedForeground)
                .padding(.horizontal, DesignSystem.Spacing.lg.rawValue)
                .padding(.vertical, DesignSystem.Spacing.sm.rawValue)
                .background(
                    isSelected ? Color.dynamicPrimary(isDark) : Color.clear
                )
                .cornerRadius(DesignSystem.Radius.full.rawValue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct VATReportView: View {
    @ObservedObject var viewModel: ReportsViewModel
    let isDark: Bool
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            // VAT Summary Cards
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: DesignSystem.Spacing.base.rawValue) {
                ReportCard(
                    title: "מע״מ שנגבה",
                    value: viewModel.formatCurrency(viewModel.vatCollected),
                    subtitle: "ממכירות",
                    icon: "arrow.up.circle.fill",
                    color: Color.success,
                    isDark: isDark
                )
                
                ReportCard(
                    title: "מע״מ ששולם",
                    value: viewModel.formatCurrency(viewModel.vatPaid),
                    subtitle: "מרכישות",
                    icon: "arrow.down.circle.fill",
                    color: Color.destructive,
                    isDark: isDark
                )
                
                ReportCard(
                    title: "יתרת מע״מ",
                    value: viewModel.formatCurrency(viewModel.vatLiability),
                    subtitle: viewModel.vatLiability >= 0 ? "לתשלום" : "להחזר",
                    icon: "equal.circle.fill",
                    color: viewModel.vatLiability >= 0 ? Color.warning : Color.success,
                    isDark: isDark
                )
                
                ReportCard(
                    title: "תאריך תשלום",
                    value: viewModel.nextVATPaymentDate,
                    subtitle: "מועד אחרון",
                    icon: "calendar.circle.fill",
                    color: Color.cosmicAccent,
                    isDark: isDark
                )
            }
            
            // VAT Details
            if !viewModel.isLoading {
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.base.rawValue) {
                    Text("פירוט מע״מ")
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                    
                    VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                        VATDetailRow(
                            title: "מכירות (לפני מע״מ)",
                            amount: viewModel.salesBeforeVAT,
                            isDark: isDark
                        )
                        
                        VATDetailRow(
                            title: "מע״מ על מכירות (18%)",
                            amount: viewModel.vatCollected,
                            isDark: isDark
                        )
                        
                        Divider()
                            .background(Color.dynamicBorder(isDark))
                        
                        VATDetailRow(
                            title: "רכישות (לפני מע״מ)",
                            amount: viewModel.purchasesBeforeVAT,
                            isDark: isDark
                        )
                        
                        VATDetailRow(
                            title: "מע״מ על רכישות",
                            amount: viewModel.vatPaid,
                            isDark: isDark
                        )
                        
                        Divider()
                            .background(Color.dynamicBorder(isDark))
                        
                        VATDetailRow(
                            title: "יתרת מע״מ לתשלום",
                            amount: viewModel.vatLiability,
                            isDark: isDark,
                            isTotal: true
                        )
                    }
                    .padding(DesignSystem.Spacing.base.rawValue)
                    .cardStyle(isDark: isDark)
                }
            }
        }
    }
}

struct VATDetailRow: View {
    let title: String
    let amount: Double
    let isDark: Bool
    let isTotal: Bool
    
    init(title: String, amount: Double, isDark: Bool, isTotal: Bool = false) {
        self.title = title
        self.amount = amount
        self.isDark = isDark
        self.isTotal = isTotal
    }
    
    var body: some View {
        HStack {
            Text(formatCurrency(amount))
                .font(.hebrewFont(size: isTotal ? .large : .base, weight: isTotal ? .bold : .medium))
                .foregroundColor(Color.dynamicForeground(isDark))
            
            Spacer()
            
            Text(title)
                .font(.hebrewFont(size: isTotal ? .large : .base, weight: isTotal ? .semibold : .medium))
                .foregroundColor(Color.dynamicForeground(isDark))
        }
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.locale = Locale(identifier: "he_IL")
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
}

struct ReportCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    let isDark: Bool
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Spacer()
                
                Text(title)
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(value)
                    .font(.hebrewFont(size: .xl, weight: .bold))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                Text(subtitle)
                    .font(.hebrewFont(size: .xs))
                    .foregroundColor(Color.mutedForeground)
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
}

// Placeholder views for other report types
struct IncomeReportView: View {
    @ObservedObject var viewModel: ReportsViewModel
    let isDark: Bool
    
    var body: some View {
        VStack {
            Text("דוח הכנסות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .cardStyle(isDark: isDark)
    }
}

struct ExpensesReportView: View {
    @ObservedObject var viewModel: ReportsViewModel
    let isDark: Bool
    
    var body: some View {
        VStack {
            Text("דוח הוצאות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .cardStyle(isDark: isDark)
    }
}

struct CustomersReportView: View {
    @ObservedObject var viewModel: ReportsViewModel
    let isDark: Bool
    
    var body: some View {
        VStack {
            Text("דוח לקוחות")
                .font(.hebrewFont(size: .xl, weight: .semibold))
            Text("תכונה זו תהיה זמינה בקרוב")
                .font(.hebrewFont(size: .base))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .cardStyle(isDark: isDark)
    }
}

// MARK: - Reports ViewModel

@MainActor
class ReportsViewModel: ObservableObject {
    @Published var vatCollected: Double = 0
    @Published var vatPaid: Double = 0
    @Published var vatLiability: Double = 0
    @Published var salesBeforeVAT: Double = 0
    @Published var purchasesBeforeVAT: Double = 0
    @Published var nextVATPaymentDate: String = ""
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadReports(companyId: UUID? = nil, period: ReportsView.ReportPeriod) async {
        guard let companyId = companyId else { return }
        
        isLoading = true
        errorMessage = nil
        
        // Simulate loading reports data
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Mock data for demonstration
        salesBeforeVAT = 50000
        vatCollected = salesBeforeVAT * 0.18
        purchasesBeforeVAT = 20000
        vatPaid = purchasesBeforeVAT * 0.18
        vatLiability = vatCollected - vatPaid
        
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        formatter.locale = Locale(identifier: "he_IL")
        nextVATPaymentDate = formatter.string(from: Date().addingTimeInterval(30 * 24 * 60 * 60))
        
        isLoading = false
    }
    
    func refreshReports() async {
        // Implement refresh logic
    }
    
    func exportReports() async {
        // Implement export functionality
        print("Exporting reports...")
    }
    
    func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.locale = Locale(identifier: "he_IL")
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
}

#Preview {
    ReportsView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
