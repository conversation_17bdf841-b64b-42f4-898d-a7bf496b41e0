//
//  AppModels.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation

// MARK: - Core App Models

struct AppConfiguration {
    static let supabaseURL = ProcessInfo.processInfo.environment["SUPABASE_URL"] ?? "https://zhwqtgypoueykwgphmqn.supabase.co"
    static let supabaseAnonKey = ProcessInfo.processInfo.environment["SUPABASE_ANON_KEY"] ?? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpod3F0Z3lwb3VleWt3Z3BobXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU0NzI4NzQsImV4cCI6MjA1MTA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    static let appVersion = "1.0.0"
    static let buildNumber = "1"
}

// MARK: - Navigation Models

enum AppTab: Int, CaseIterable {
    case dashboard = 0
    case documents = 1
    case expenses = 2
    case reports = 3
    case settings = 4
    
    var title: String {
        switch self {
        case .dashboard: return "לוח בקרה"
        case .documents: return "מסמכים"
        case .expenses: return "הוצאות"
        case .reports: return "דוחות"
        case .settings: return "הגדרות"
        }
    }
    
    var icon: String {
        switch self {
        case .dashboard: return "house.fill"
        case .documents: return "doc.text.fill"
        case .expenses: return "creditcard.fill"
        case .reports: return "chart.bar.fill"
        case .settings: return "gearshape.fill"
        }
    }
}

// MARK: - UI State Models

struct LoadingState {
    var isLoading: Bool = false
    var message: String? = nil
    
    mutating func startLoading(message: String? = nil) {
        isLoading = true
        self.message = message
    }
    
    mutating func stopLoading() {
        isLoading = false
        message = nil
    }
}

struct ErrorState {
    var hasError: Bool = false
    var message: String? = nil
    var code: String? = nil
    
    mutating func setError(_ error: Error, code: String? = nil) {
        hasError = true
        message = error.localizedDescription
        self.code = code
    }
    
    mutating func clearError() {
        hasError = false
        message = nil
        code = nil
    }
}

// MARK: - Form Models

struct ValidationResult {
    let isValid: Bool
    let errors: [String]
    
    static let valid = ValidationResult(isValid: true, errors: [])
    
    static func invalid(_ errors: [String]) -> ValidationResult {
        return ValidationResult(isValid: false, errors: errors)
    }
    
    static func invalid(_ error: String) -> ValidationResult {
        return ValidationResult(isValid: false, errors: [error])
    }
}

// MARK: - Business Logic Models

struct BusinessMetrics {
    let monthlyRevenue: Double
    let monthlyExpenses: Double
    let vatLiability: Double
    let openInvoicesCount: Int
    let openInvoicesAmount: Double
    let pendingExpensesCount: Int
    let customerCount: Int
    let averageInvoiceAmount: Double
    
    var netIncome: Double {
        return monthlyRevenue - monthlyExpenses
    }
    
    var profitMargin: Double {
        guard monthlyRevenue > 0 else { return 0 }
        return (netIncome / monthlyRevenue) * 100
    }
}

struct ReportData {
    let period: DateInterval
    let salesData: [SalesDataPoint]
    let expenseData: [ExpenseDataPoint]
    let vatData: VATData
    let customerData: [CustomerDataPoint]
}

struct SalesDataPoint {
    let date: Date
    let amount: Double
    let invoiceCount: Int
}

struct ExpenseDataPoint {
    let date: Date
    let amount: Double
    let category: ExpenseCategory
    let expenseCount: Int
}

struct VATData {
    let collected: Double
    let paid: Double
    let liability: Double
    let nextPaymentDate: Date
    let salesBeforeVAT: Double
    let purchasesBeforeVAT: Double
}

struct CustomerDataPoint {
    let customer: Customer
    let totalRevenue: Double
    let invoiceCount: Int
    let lastInvoiceDate: Date?
}

// MARK: - File and Media Models

struct DocumentFile {
    let id: UUID
    let name: String
    let url: URL
    let mimeType: String
    let size: Int64
    let uploadedAt: Date
}

struct ScannedDocument {
    let images: [UIImage]
    let extractedText: String?
    let confidence: Double
    let boundingBoxes: [CGRect]
}

// MARK: - Notification Models

struct AppNotification {
    let id: UUID
    let title: String
    let message: String
    let type: NotificationType
    let createdAt: Date
    let isRead: Bool
    let actionURL: URL?
}

enum NotificationType: String, CaseIterable {
    case expenseApproval = "expense_approval"
    case invoiceOverdue = "invoice_overdue"
    case vatReminder = "vat_reminder"
    case systemUpdate = "system_update"
    case paymentReceived = "payment_received"
    
    var icon: String {
        switch self {
        case .expenseApproval: return "checkmark.circle.fill"
        case .invoiceOverdue: return "exclamationmark.triangle.fill"
        case .vatReminder: return "calendar.badge.exclamationmark"
        case .systemUpdate: return "arrow.clockwise.circle.fill"
        case .paymentReceived: return "creditcard.fill"
        }
    }
    
    var color: String {
        switch self {
        case .expenseApproval: return "success"
        case .invoiceOverdue: return "destructive"
        case .vatReminder: return "warning"
        case .systemUpdate: return "cosmicAccent"
        case .paymentReceived: return "success"
        }
    }
}

// MARK: - Settings Models

struct AppSettings {
    var isDarkMode: Bool = true
    var language: AppLanguage = .hebrew
    var currency: Currency = .ils
    var dateFormat: DateFormat = .ddmmyyyy
    var notificationsEnabled: Bool = true
    var biometricAuthEnabled: Bool = false
    var autoBackupEnabled: Bool = true
    var offlineModeEnabled: Bool = true
}

enum AppLanguage: String, CaseIterable {
    case hebrew = "he"
    case english = "en"
    
    var displayName: String {
        switch self {
        case .hebrew: return "עברית"
        case .english: return "English"
        }
    }
}

enum Currency: String, CaseIterable {
    case ils = "ILS"
    case usd = "USD"
    case eur = "EUR"
    
    var symbol: String {
        switch self {
        case .ils: return "₪"
        case .usd: return "$"
        case .eur: return "€"
        }
    }
    
    var displayName: String {
        switch self {
        case .ils: return "שקל ישראלי"
        case .usd: return "דולר אמריקאי"
        case .eur: return "יורו"
        }
    }
}

enum DateFormat: String, CaseIterable {
    case ddmmyyyy = "dd/MM/yyyy"
    case mmddyyyy = "MM/dd/yyyy"
    case yyyymmdd = "yyyy-MM-dd"
    
    var displayName: String {
        switch self {
        case .ddmmyyyy: return "יום/חודש/שנה"
        case .mmddyyyy: return "חודש/יום/שנה"
        case .yyyymmdd: return "שנה-חודש-יום"
        }
    }
}

// MARK: - Analytics Models

struct AnalyticsEvent {
    let name: String
    let parameters: [String: Any]
    let timestamp: Date
    let userId: UUID?
    let companyId: UUID?
}

struct UserSession {
    let sessionId: UUID
    let userId: UUID
    let startTime: Date
    var endTime: Date?
    var screenViews: [ScreenView] = []
    var actions: [UserAction] = []
}

struct ScreenView {
    let screenName: String
    let timestamp: Date
    let duration: TimeInterval?
}

struct UserAction {
    let actionName: String
    let timestamp: Date
    let parameters: [String: Any]
}

// MARK: - Sync Models

struct SyncStatus {
    var lastSyncTime: Date?
    var pendingUploads: Int = 0
    var pendingDownloads: Int = 0
    var isSyncing: Bool = false
    var syncErrors: [SyncError] = []
}

struct SyncError {
    let id: UUID
    let message: String
    let timestamp: Date
    let retryCount: Int
    let maxRetries: Int = 3
    
    var canRetry: Bool {
        return retryCount < maxRetries
    }
}

// MARK: - Cache Models

struct CacheEntry<T: Codable> {
    let data: T
    let timestamp: Date
    let expirationTime: TimeInterval
    
    var isExpired: Bool {
        return Date().timeIntervalSince(timestamp) > expirationTime
    }
}

// MARK: - Helper Extensions

extension DateInterval {
    static func currentMonth() -> DateInterval {
        let calendar = Calendar.current
        let now = Date()
        return calendar.dateInterval(of: .month, for: now) ?? DateInterval(start: now, end: now)
    }
    
    static func lastMonth() -> DateInterval {
        let calendar = Calendar.current
        let now = Date()
        let lastMonth = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        return calendar.dateInterval(of: .month, for: lastMonth) ?? DateInterval(start: now, end: now)
    }
    
    static func currentYear() -> DateInterval {
        let calendar = Calendar.current
        let now = Date()
        return calendar.dateInterval(of: .year, for: now) ?? DateInterval(start: now, end: now)
    }
}

extension NumberFormatter {
    static func currency(for currency: Currency) -> NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency.rawValue
        formatter.currencySymbol = currency.symbol
        formatter.locale = Locale(identifier: currency == .ils ? "he_IL" : "en_US")
        return formatter
    }
    
    static func percentage() -> NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .percent
        formatter.minimumFractionDigits = 1
        formatter.maximumFractionDigits = 1
        return formatter
    }
}
