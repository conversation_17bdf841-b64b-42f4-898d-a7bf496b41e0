//
//  DashboardViewModel.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import Combine

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var monthlyRevenue: Double = 0
    @Published var vatLiability: Double = 0
    @Published var openInvoicesCount: Int = 0
    @Published var openInvoicesAmount: Double = 0
    @Published var pendingExpensesCount: Int = 0
    @Published var recentDocuments: [Document] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Computed properties for UI
    var revenueChangeText: String {
        // This would typically compare to previous month
        "+12% מהחודש הקודם"
    }
    
    var nextPaymentDateText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM"
        formatter.locale = Locale(identifier: "he_IL")
        
        // Calculate next VAT payment date (typically 15th of next month)
        let calendar = Calendar.current
        let nextMonth = calendar.date(byAdding: .month, value: 1, to: Date()) ?? Date()
        let nextPaymentDate = calendar.date(bySetting: .day, value: 15, of: nextMonth) ?? Date()
        
        return "תשלום עד \(formatter.string(from: nextPaymentDate))"
    }
    
    func loadDashboardData(companyId: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load dashboard stats
            let stats = try await supabaseService.getDashboardStats(companyId: companyId)
            
            monthlyRevenue = stats.monthlyRevenue
            vatLiability = stats.vatLiability
            pendingExpensesCount = stats.pendingExpensesCount
            
            // Load recent documents
            let documents = try await supabaseService.getDocuments(companyId: companyId, limit: 5)
            recentDocuments = documents
            
            // Calculate open invoices
            let openInvoices = documents.filter { $0.status == .sent || $0.status == .approved }
            openInvoicesCount = openInvoices.count
            openInvoicesAmount = openInvoices.reduce(0) { $0 + $1.totalAmount }
            
        } catch {
            errorMessage = "שגיאה בטעינת נתונים: \(error.localizedDescription)"
            print("Dashboard loading error: \(error)")
        }
        
        isLoading = false
    }
    
    func refreshData() async {
        // This would be called by pull-to-refresh
        // Implementation would reload all data
    }
    
    func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.locale = Locale(identifier: "he_IL")
        formatter.currencySymbol = "₪"
        
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
}

// MARK: - Document Model

struct Document: Identifiable, Codable {
    let id: UUID
    let companyId: UUID
    let documentType: DocumentType
    let documentNumber: String
    let customerId: UUID
    let issueDate: Date
    let dueDate: Date?
    let currency: String
    let subtotal: Double
    let vatAmount: Double
    let totalAmount: Double
    let status: DocumentStatus
    let itaAllocationNumber: String?
    let itaAllocationDate: Date?
    let notes: String?
    let pdfUrl: String?
    let sentAt: Date?
    let createdAt: Date
    let updatedAt: Date
    
    // Related data
    let customer: Customer?
    let items: [DocumentItem]?
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case documentType = "document_type"
        case documentNumber = "document_number"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case subtotal
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case status
        case itaAllocationNumber = "ita_allocation_number"
        case itaAllocationDate = "ita_allocation_date"
        case notes
        case pdfUrl = "pdf_url"
        case sentAt = "sent_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case customer
        case items
    }
}

enum DocumentType: String, Codable, CaseIterable {
    case taxInvoice = "tax_invoice"
    case receipt = "receipt"
    case creditNote = "credit_note"
    case taxInvoiceReceipt = "tax_invoice_receipt"
    
    var displayName: String {
        switch self {
        case .taxInvoice:
            return "חשבונית מס"
        case .receipt:
            return "קבלה"
        case .creditNote:
            return "זיכוי"
        case .taxInvoiceReceipt:
            return "חשבונית מס/קבלה"
        }
    }
    
    var icon: String {
        switch self {
        case .taxInvoice:
            return "doc.text.fill"
        case .receipt:
            return "receipt"
        case .creditNote:
            return "minus.circle.fill"
        case .taxInvoiceReceipt:
            return "doc.badge.plus"
        }
    }
}

enum DocumentStatus: String, Codable, CaseIterable {
    case draft = "draft"
    case pendingAllocation = "pending_allocation"
    case approved = "approved"
    case sent = "sent"
    case paid = "paid"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין לאישור"
        case .approved:
            return "אושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var color: Color {
        switch self {
        case .draft:
            return Color.mutedForeground
        case .pendingAllocation:
            return Color.warning
        case .approved:
            return Color.success
        case .sent:
            return Color.cosmicAccent
        case .paid:
            return Color.success
        case .cancelled:
            return Color.destructive
        }
    }
}

struct Customer: Identifiable, Codable {
    let id: UUID
    let companyId: UUID
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String
    let billingAddressEnglish: String?
    let shippingAddressHebrew: String?
    let shippingAddressEnglish: String?
    let cityHebrew: String
    let cityEnglish: String?
    let contactName: String?
    let contactEmail: String?
    let contactPhone: String?
    let notes: String?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case billingAddressEnglish = "billing_address_english"
        case shippingAddressHebrew = "shipping_address_hebrew"
        case shippingAddressEnglish = "shipping_address_english"
        case cityHebrew = "city_hebrew"
        case cityEnglish = "city_english"
        case contactName = "contact_name"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
        case notes
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct DocumentItem: Identifiable, Codable {
    let id: UUID
    let documentId: UUID
    let productId: UUID?
    let lineNumber: Int
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let currency: String
    let discountPercent: Double
    let vatRate: Double
    let lineTotal: Double
    let vatAmount: Double
    let totalWithVat: Double
    
    enum CodingKeys: String, CodingKey {
        case id
        case documentId = "document_id"
        case productId = "product_id"
        case lineNumber = "line_number"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case currency
        case discountPercent = "discount_percent"
        case vatRate = "vat_rate"
        case lineTotal = "line_total"
        case vatAmount = "vat_amount"
        case totalWithVat = "total_with_vat"
    }
}

struct Expense: Identifiable, Codable {
    let id: UUID
    let companyId: UUID
    let expenseNumber: String
    let vendorName: String
    let expenseDate: Date
    let amount: Double
    let vatAmount: Double
    let totalAmount: Double
    let currency: String
    let category: ExpenseCategory
    let description: String?
    let status: ExpenseStatus
    let duplicateRisk: DuplicateRisk
    let duplicateOfId: UUID?
    let source: ExpenseSource
    let sourceEmailId: UUID?
    let originalFileUrl: String?
    let extractedData: [String: Any]?
    let approvedBy: UUID?
    let approvedAt: Date?
    let rejectionReason: String?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case expenseNumber = "expense_number"
        case vendorName = "vendor_name"
        case expenseDate = "expense_date"
        case amount
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case currency
        case category
        case description
        case status
        case duplicateRisk = "duplicate_risk"
        case duplicateOfId = "duplicate_of_id"
        case source
        case sourceEmailId = "source_email_id"
        case originalFileUrl = "original_file_url"
        case extractedData = "extracted_data"
        case approvedBy = "approved_by"
        case approvedAt = "approved_at"
        case rejectionReason = "rejection_reason"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

enum ExpenseCategory: String, Codable, CaseIterable {
    case officeSupplies = "office_supplies"
    case travel = "travel"
    case utilities = "utilities"
    case rent = "rent"
    case professionalServices = "professional_services"
    case marketing = "marketing"
    case equipment = "equipment"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .officeSupplies:
            return "ציוד משרדי"
        case .travel:
            return "נסיעות"
        case .utilities:
            return "שירותים"
        case .rent:
            return "שכירות"
        case .professionalServices:
            return "שירותים מקצועיים"
        case .marketing:
            return "שיווק"
        case .equipment:
            return "ציוד"
        case .other:
            return "אחר"
        }
    }
}

enum ExpenseStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case approved = "approved"
    case rejected = "rejected"
    
    var displayName: String {
        switch self {
        case .pending:
            return "ממתין"
        case .approved:
            return "אושר"
        case .rejected:
            return "נדחה"
        }
    }
    
    var color: Color {
        switch self {
        case .pending:
            return Color.warning
        case .approved:
            return Color.success
        case .rejected:
            return Color.destructive
        }
    }
}

enum DuplicateRisk: String, Codable, CaseIterable {
    case none = "none"
    case low = "low"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .none:
            return "ללא"
        case .low:
            return "נמוך"
        case .high:
            return "גבוה"
        }
    }
}

enum ExpenseSource: String, Codable, CaseIterable {
    case manual = "manual"
    case emailScan = "email_scan"
    case upload = "upload"
    
    var displayName: String {
        switch self {
        case .manual:
            return "ידני"
        case .emailScan:
            return "סריקת אימייל"
        case .upload:
            return "העלאה"
        }
    }
}
