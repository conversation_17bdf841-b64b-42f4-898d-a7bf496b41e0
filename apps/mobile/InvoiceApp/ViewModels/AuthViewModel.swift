//
//  AuthViewModel.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import Combine
import Supabase

@MainActor
class AuthViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var currentUser: User?
    @Published var currentCompany: Company?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupAuthStateListener()
    }
    
    private func setupAuthStateListener() {
        supabaseService.authStatePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] session in
                self?.isAuthenticated = session != nil
                if session == nil {
                    self?.currentUser = nil
                    self?.currentCompany = nil
                }
            }
            .store(in: &cancellables)
    }
    
    func checkAuthenticationStatus() {
        Task {
            do {
                let session = try await supabaseService.getCurrentSession()
                isAuthenticated = session != nil
                if let session = session {
                    await loadUserData(userId: session.user.id)
                }
            } catch {
                isAuthenticated = false
                print("Auth check error: \(error)")
            }
        }
    }
    
    func signIn(email: String, password: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let session = try await supabaseService.signIn(email: email, password: password)
            await loadUserData(userId: session.user.id)
            isAuthenticated = true
        } catch {
            errorMessage = getErrorMessage(from: error)
        }
        
        isLoading = false
    }
    
    func signUp(registrationData: RegistrationData) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let result = try await supabaseService.signUp(registrationData: registrationData)
            currentUser = result.user
            currentCompany = result.company
            isAuthenticated = true
        } catch {
            errorMessage = getErrorMessage(from: error)
        }
        
        isLoading = false
    }
    
    func signOut() async {
        isLoading = true
        
        do {
            try await supabaseService.signOut()
            isAuthenticated = false
            currentUser = nil
            currentCompany = nil
        } catch {
            errorMessage = getErrorMessage(from: error)
        }
        
        isLoading = false
    }
    
    private func loadUserData(userId: UUID) async {
        do {
            let userData = try await supabaseService.getUserData(userId: userId)
            currentUser = userData.user
            currentCompany = userData.companies.first
        } catch {
            print("Failed to load user data: \(error)")
        }
    }
    
    private func getErrorMessage(from error: Error) -> String {
        if let authError = error as? AuthError {
            switch authError {
            case .invalidCredentials:
                return "כתובת אימייל או סיסמה שגויים"
            case .emailAlreadyExists:
                return "כתובת האימייל כבר קיימת במערכת"
            case .weakPassword:
                return "הסיסמה חלשה מדי"
            case .networkError:
                return "בעיית רשת - נסה שוב"
            default:
                return "שגיאה לא צפויה - נסה שוב"
            }
        }
        return error.localizedDescription
    }
}

// MARK: - Data Models
struct RegistrationData {
    let email: String
    let password: String
    let fullName: String
    let phone: String
    let company: CompanyRegistrationData
}

struct CompanyRegistrationData {
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let addressHebrew: String
    let cityHebrew: String
    let phone: String
    let industry: String
    let annualRevenue: String?
    let interestedInLoan: Bool
    let interestedInInsurance: Bool
    let interestedInAccounting: Bool
}

struct User: Codable, Identifiable {
    let id: UUID
    let email: String
    let fullName: String
    let phone: String?
    let createdAt: Date
    let lastLoginAt: Date?
    
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case phone
        case createdAt = "created_at"
        case lastLoginAt = "last_login_at"
    }
}

struct Company: Codable, Identifiable {
    let id: UUID
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String
    let addressHebrew: String
    let addressEnglish: String?
    let cityHebrew: String
    let cityEnglish: String?
    let postalCode: String?
    let phone: String
    let email: String
    let logoUrl: String?
    let subscriptionTier: SubscriptionTier
    let subscriptionExpiresAt: Date?
    let industry: String
    let annualRevenue: String?
    let interestedInLoan: Bool
    let interestedInInsurance: Bool
    let interestedInAccounting: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case addressHebrew = "address_hebrew"
        case addressEnglish = "address_english"
        case cityHebrew = "city_hebrew"
        case cityEnglish = "city_english"
        case postalCode = "postal_code"
        case phone
        case email
        case logoUrl = "logo_url"
        case subscriptionTier = "subscription_tier"
        case subscriptionExpiresAt = "subscription_expires_at"
        case industry
        case annualRevenue = "annual_revenue"
        case interestedInLoan = "interested_in_loan"
        case interestedInInsurance = "interested_in_insurance"
        case interestedInAccounting = "interested_in_accounting"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

enum SubscriptionTier: String, Codable, CaseIterable {
    case free = "free"
    case paid = "paid"
    
    var displayName: String {
        switch self {
        case .free:
            return "חינם"
        case .paid:
            return "מתקדם"
        }
    }
}

enum AuthError: Error {
    case invalidCredentials
    case emailAlreadyExists
    case weakPassword
    case networkError
    case unknown
}

struct UserDataResponse {
    let user: User
    let companies: [Company]
}
