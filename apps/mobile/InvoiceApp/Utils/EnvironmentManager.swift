//
//  EnvironmentManager.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation

class EnvironmentManager {
    static let shared = EnvironmentManager()
    
    private init() {}
    
    // MARK: - Supabase Configuration
    
    var supabaseURL: String {
        return "https://zhwqtgypoueykwgphmqn.supabase.co"
    }

    var supabaseAnonKey: String {
        return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpod3F0Z3lwb3VleWt3Z3BobXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY0NjgyOTQsImV4cCI6MjA3MjA0NDI5NH0.OO6IgJ4oZQvP0T0lJd5OUgN2_apHT9xqpGhZbHXy0lM"
    }
    
    // MARK: - App Configuration
    
    var isDebugMode: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
    
    var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    var buildNumber: String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    var bundleIdentifier: String {
        return Bundle.main.bundleIdentifier ?? "com.invoiceapp.mobile"
    }
    
    // MARK: - Feature Flags
    
    var isOfflineModeEnabled: Bool {
        return UserDefaults.standard.bool(forKey: "offline_mode_enabled")
    }
    
    var isAnalyticsEnabled: Bool {
        return !isDebugMode && UserDefaults.standard.bool(forKey: "analytics_enabled")
    }
    
    var isCrashReportingEnabled: Bool {
        return !isDebugMode && UserDefaults.standard.bool(forKey: "crash_reporting_enabled")
    }
    
    var isAdvancedFeaturesEnabled: Bool {
        return UserDefaults.standard.bool(forKey: "advanced_features_enabled")
    }
    
    // MARK: - API Configuration
    
    var apiTimeout: TimeInterval {
        return isDebugMode ? 60.0 : 30.0
    }
    
    var maxRetryAttempts: Int {
        return 3
    }
    
    var cacheExpirationTime: TimeInterval {
        return 300 // 5 minutes
    }
    
    // MARK: - Security Configuration
    
    var certificatePinningEnabled: Bool {
        return !isDebugMode
    }
    
    var biometricAuthEnabled: Bool {
        return UserDefaults.standard.bool(forKey: "biometric_auth_enabled")
    }
    
    var sessionTimeout: TimeInterval {
        return 3600 // 1 hour
    }
    
    // MARK: - UI Configuration
    
    var animationDuration: TimeInterval {
        return 0.3
    }
    
    var hapticFeedbackEnabled: Bool {
        return UserDefaults.standard.bool(forKey: "haptic_feedback_enabled")
    }
    
    var reducedMotionEnabled: Bool {
        return UIAccessibility.isReduceMotionEnabled
    }
    
    // MARK: - Logging Configuration
    
    var logLevel: LogLevel {
        if isDebugMode {
            return .debug
        } else {
            return .error
        }
    }
    
    var maxLogFileSize: Int {
        return 10 * 1024 * 1024 // 10 MB
    }
    
    var maxLogFiles: Int {
        return 5
    }
}

enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    case critical = 4
    
    var description: String {
        switch self {
        case .debug: return "DEBUG"
        case .info: return "INFO"
        case .warning: return "WARNING"
        case .error: return "ERROR"
        case .critical: return "CRITICAL"
        }
    }
}

// MARK: - Logger

class Logger {
    static let shared = Logger()
    private let environment = EnvironmentManager.shared
    
    private init() {}
    
    func log(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        guard level.rawValue >= environment.logLevel.rawValue else { return }
        
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        let timestamp = DateFormatter.logFormatter.string(from: Date())
        
        let logMessage = "[\(timestamp)] [\(level.description)] [\(fileName):\(line)] \(function) - \(message)"
        
        if environment.isDebugMode {
            print(logMessage)
        }
        
        // In production, you would write to a log file or send to a logging service
        writeToLogFile(logMessage)
    }
    
    func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .debug, file: file, function: function, line: line)
    }
    
    func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .info, file: file, function: function, line: line)
    }
    
    func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .warning, file: file, function: function, line: line)
    }
    
    func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .error, file: file, function: function, line: line)
    }
    
    func critical(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .critical, file: file, function: function, line: line)
    }
    
    private func writeToLogFile(_ message: String) {
        // Implementation for writing to log file
        // This would be implemented in a production app
    }
}

extension DateFormatter {
    static let logFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()
}

// MARK: - Performance Monitor

class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    private var startTimes: [String: Date] = [:]
    
    private init() {}
    
    func startMeasuring(_ identifier: String) {
        startTimes[identifier] = Date()
        Logger.shared.debug("Started measuring: \(identifier)")
    }
    
    func endMeasuring(_ identifier: String) -> TimeInterval? {
        guard let startTime = startTimes[identifier] else {
            Logger.shared.warning("No start time found for identifier: \(identifier)")
            return nil
        }
        
        let duration = Date().timeIntervalSince(startTime)
        startTimes.removeValue(forKey: identifier)
        
        Logger.shared.debug("Finished measuring: \(identifier) - Duration: \(duration)s")
        
        // Log performance metrics for monitoring
        if duration > 1.0 {
            Logger.shared.warning("Slow operation detected: \(identifier) took \(duration)s")
        }
        
        return duration
    }
    
    func measure<T>(_ identifier: String, operation: () throws -> T) rethrows -> T {
        startMeasuring(identifier)
        defer { endMeasuring(identifier) }
        return try operation()
    }
    
    func measureAsync<T>(_ identifier: String, operation: () async throws -> T) async rethrows -> T {
        startMeasuring(identifier)
        defer { endMeasuring(identifier) }
        return try await operation()
    }
}

// MARK: - Network Monitor

import Network

class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    @Published var isConnected = true
    @Published var connectionType: NWInterface.InterfaceType?
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    private init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.connectionType = path.availableInterfaces.first?.type
                
                Logger.shared.info("Network status changed: \(path.status == .satisfied ? "Connected" : "Disconnected")")
            }
        }
        
        monitor.start(queue: queue)
    }
    
    deinit {
        monitor.cancel()
    }
}

// MARK: - Device Info

struct DeviceInfo {
    static var modelName: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value))!)
        }
        return identifier
    }
    
    static var systemVersion: String {
        return UIDevice.current.systemVersion
    }
    
    static var appVersion: String {
        return EnvironmentManager.shared.appVersion
    }
    
    static var buildNumber: String {
        return EnvironmentManager.shared.buildNumber
    }
    
    static var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    static var screenSize: CGSize {
        return UIScreen.main.bounds.size
    }
    
    static var hasNotch: Bool {
        guard let window = UIApplication.shared.windows.first else { return false }
        return window.safeAreaInsets.top > 20
    }
}

// MARK: - Keychain Helper

import Security

class KeychainHelper {
    static let shared = KeychainHelper()
    
    private init() {}
    
    func save(_ data: Data, for key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        SecItemDelete(query as CFDictionary)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    func load(for key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else { return nil }
        return result as? Data
    }
    
    func delete(for key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess
    }
}

// MARK: - Biometric Authentication

import LocalAuthentication

class BiometricAuthManager {
    static let shared = BiometricAuthManager()
    
    private init() {}
    
    var biometryType: LABiometryType {
        let context = LAContext()
        _ = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil)
        return context.biometryType
    }
    
    var isBiometricAvailable: Bool {
        let context = LAContext()
        var error: NSError?
        return context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
    }
    
    func authenticateWithBiometrics() async throws -> Bool {
        let context = LAContext()
        context.localizedCancelTitle = "ביטול"
        
        let reason = "השתמש בזיהוי ביומטרי כדי לגשת לחשבון שלך"
        
        do {
            let result = try await context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason)
            return result
        } catch {
            Logger.shared.error("Biometric authentication failed: \(error)")
            throw error
        }
    }
}
