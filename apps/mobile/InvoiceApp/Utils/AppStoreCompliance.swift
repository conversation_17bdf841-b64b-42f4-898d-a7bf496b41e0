//
//  AppStoreCompliance.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation
import UIKit
import StoreKit

// MARK: - App Store Compliance Manager

class AppStoreComplianceManager: ObservableObject {
    static let shared = AppStoreComplianceManager()
    
    @Published var privacyPolicyAccepted = false
    @Published var termsOfServiceAccepted = false
    @Published var dataProcessingConsent = false
    @Published var marketingConsent = false
    
    private let userDefaults = UserDefaults.standard
    
    private init() {
        loadConsentStatus()
    }
    
    // MARK: - Privacy Compliance
    
    func loadConsentStatus() {
        privacyPolicyAccepted = userDefaults.bool(forKey: "privacy_policy_accepted")
        termsOfServiceAccepted = userDefaults.bool(forKey: "terms_of_service_accepted")
        dataProcessingConsent = userDefaults.bool(forKey: "data_processing_consent")
        marketingConsent = userDefaults.bool(forKey: "marketing_consent")
    }
    
    func acceptPrivacyPolicy() {
        privacyPolicyAccepted = true
        userDefaults.set(true, forKey: "privacy_policy_accepted")
        userDefaults.set(Date(), forKey: "privacy_policy_accepted_date")
        Logger.shared.info("Privacy policy accepted")
    }
    
    func acceptTermsOfService() {
        termsOfServiceAccepted = true
        userDefaults.set(true, forKey: "terms_of_service_accepted")
        userDefaults.set(Date(), forKey: "terms_of_service_accepted_date")
        Logger.shared.info("Terms of service accepted")
    }
    
    func setDataProcessingConsent(_ consent: Bool) {
        dataProcessingConsent = consent
        userDefaults.set(consent, forKey: "data_processing_consent")
        userDefaults.set(Date(), forKey: "data_processing_consent_date")
        Logger.shared.info("Data processing consent: \(consent)")
    }
    
    func setMarketingConsent(_ consent: Bool) {
        marketingConsent = consent
        userDefaults.set(consent, forKey: "marketing_consent")
        userDefaults.set(Date(), forKey: "marketing_consent_date")
        Logger.shared.info("Marketing consent: \(consent)")
    }
    
    var hasRequiredConsents: Bool {
        return privacyPolicyAccepted && termsOfServiceAccepted && dataProcessingConsent
    }
    
    // MARK: - App Store Review
    
    func requestAppStoreReview() {
        guard let scene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene else {
            return
        }
        
        SKStoreReviewController.requestReview(in: scene)
        Logger.shared.info("App Store review requested")
    }
    
    func shouldRequestReview() -> Bool {
        let reviewRequestCount = userDefaults.integer(forKey: "review_request_count")
        let lastReviewRequestDate = userDefaults.object(forKey: "last_review_request_date") as? Date
        
        // Don't request more than 3 times per year
        if reviewRequestCount >= 3 {
            if let lastDate = lastReviewRequestDate,
               Date().timeIntervalSince(lastDate) < 365 * 24 * 60 * 60 {
                return false
            }
        }
        
        // Check if user has used the app enough
        let appLaunchCount = userDefaults.integer(forKey: "app_launch_count")
        let documentsCreated = userDefaults.integer(forKey: "documents_created_count")
        
        return appLaunchCount >= 10 && documentsCreated >= 5
    }
    
    func recordReviewRequest() {
        let currentCount = userDefaults.integer(forKey: "review_request_count")
        userDefaults.set(currentCount + 1, forKey: "review_request_count")
        userDefaults.set(Date(), forKey: "last_review_request_date")
    }
    
    // MARK: - Usage Analytics (Privacy-Safe)
    
    func recordAppLaunch() {
        let currentCount = userDefaults.integer(forKey: "app_launch_count")
        userDefaults.set(currentCount + 1, forKey: "app_launch_count")
        
        // Check if we should request review
        if shouldRequestReview() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.requestAppStoreReview()
                self.recordReviewRequest()
            }
        }
    }
    
    func recordDocumentCreated() {
        let currentCount = userDefaults.integer(forKey: "documents_created_count")
        userDefaults.set(currentCount + 1, forKey: "documents_created_count")
    }
    
    func recordExpenseScanned() {
        let currentCount = userDefaults.integer(forKey: "expenses_scanned_count")
        userDefaults.set(currentCount + 1, forKey: "expenses_scanned_count")
    }
    
    // MARK: - Data Export (GDPR Compliance)
    
    func exportUserData() -> [String: Any] {
        var userData: [String: Any] = [:]
        
        // App usage data
        userData["app_launch_count"] = userDefaults.integer(forKey: "app_launch_count")
        userData["documents_created_count"] = userDefaults.integer(forKey: "documents_created_count")
        userData["expenses_scanned_count"] = userDefaults.integer(forKey: "expenses_scanned_count")
        
        // Consent data
        userData["privacy_policy_accepted"] = privacyPolicyAccepted
        userData["terms_of_service_accepted"] = termsOfServiceAccepted
        userData["data_processing_consent"] = dataProcessingConsent
        userData["marketing_consent"] = marketingConsent
        
        if let privacyDate = userDefaults.object(forKey: "privacy_policy_accepted_date") as? Date {
            userData["privacy_policy_accepted_date"] = ISO8601DateFormatter().string(from: privacyDate)
        }
        
        if let termsDate = userDefaults.object(forKey: "terms_of_service_accepted_date") as? Date {
            userData["terms_of_service_accepted_date"] = ISO8601DateFormatter().string(from: termsDate)
        }
        
        // App settings
        userData["dark_mode_enabled"] = ThemeManager().isDarkMode
        userData["biometric_auth_enabled"] = EnvironmentManager.shared.biometricAuthEnabled
        userData["haptic_feedback_enabled"] = EnvironmentManager.shared.hapticFeedbackEnabled
        
        return userData
    }
    
    func deleteAllUserData() {
        // Remove all user defaults
        let keys = [
            "app_launch_count", "documents_created_count", "expenses_scanned_count",
            "privacy_policy_accepted", "terms_of_service_accepted",
            "data_processing_consent", "marketing_consent",
            "privacy_policy_accepted_date", "terms_of_service_accepted_date",
            "data_processing_consent_date", "marketing_consent_date",
            "review_request_count", "last_review_request_date",
            "isDarkMode", "biometric_auth_enabled", "haptic_feedback_enabled"
        ]
        
        for key in keys {
            userDefaults.removeObject(forKey: key)
        }
        
        // Clear keychain
        _ = KeychainHelper.shared.delete(for: "stored_email")
        
        Logger.shared.info("All user data deleted")
    }
}

// MARK: - Privacy Notice View

struct PrivacyNoticeView: View {
    @ObservedObject var complianceManager = AppStoreComplianceManager.shared
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingPrivacyPolicy = false
    @State private var showingTermsOfService = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
                    // Header
                    VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                        Image(systemName: "hand.raised.fill")
                            .font(.system(size: 60))
                            .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                        
                        VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                            Text("פרטיות ותנאי שימוש")
                                .font(.hebrewFont(size: .xxxl, weight: .bold))
                                .foregroundColor(Color.dynamicForeground(themeManager.isDarkMode))
                            
                            Text("אנא קרא ואשר את התנאים הבאים")
                                .font(.hebrewFont(size: .base))
                                .foregroundColor(Color.mutedForeground)
                                .multilineTextAlignment(.center)
                        }
                    }
                    
                    // Privacy Policy
                    VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                        Button(action: { showingPrivacyPolicy = true }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                    .foregroundColor(Color.mutedForeground)
                                
                                Spacer()
                                
                                Text("מדיניות פרטיות")
                                    .font(.hebrewFont(size: .base, weight: .medium))
                                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            }
                            .padding(DesignSystem.Spacing.base.rawValue)
                            .cardStyle(isDark: themeManager.isDarkMode)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        ToggleField(
                            title: "אני מסכים למדיניות הפרטיות",
                            subtitle: "נדרש לשימוש באפליקציה",
                            isOn: $complianceManager.privacyPolicyAccepted,
                            isDark: themeManager.isDarkMode
                        )
                        .onChange(of: complianceManager.privacyPolicyAccepted) { accepted in
                            if accepted {
                                complianceManager.acceptPrivacyPolicy()
                            }
                        }
                    }
                    
                    // Terms of Service
                    VStack(spacing: DesignSystem.Spacing.base.rawValue) {
                        Button(action: { showingTermsOfService = true }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                    .foregroundColor(Color.mutedForeground)
                                
                                Spacer()
                                
                                Text("תנאי שימוש")
                                    .font(.hebrewFont(size: .base, weight: .medium))
                                    .foregroundColor(Color.dynamicPrimary(themeManager.isDarkMode))
                            }
                            .padding(DesignSystem.Spacing.base.rawValue)
                            .cardStyle(isDark: themeManager.isDarkMode)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        ToggleField(
                            title: "אני מסכים לתנאי השימוש",
                            subtitle: "נדרש לשימוש באפליקציה",
                            isOn: $complianceManager.termsOfServiceAccepted,
                            isDark: themeManager.isDarkMode
                        )
                        .onChange(of: complianceManager.termsOfServiceAccepted) { accepted in
                            if accepted {
                                complianceManager.acceptTermsOfService()
                            }
                        }
                    }
                    
                    // Data Processing Consent
                    ToggleField(
                        title: "הסכמה לעיבוד נתונים",
                        subtitle: "נדרש לתפעול האפליקציה",
                        isOn: $complianceManager.dataProcessingConsent,
                        isDark: themeManager.isDarkMode
                    )
                    .onChange(of: complianceManager.dataProcessingConsent) { consent in
                        complianceManager.setDataProcessingConsent(consent)
                    }
                    
                    // Marketing Consent (Optional)
                    ToggleField(
                        title: "הסכמה לקבלת עדכונים",
                        subtitle: "אופציונלי - עדכונים על תכונות חדשות",
                        isOn: $complianceManager.marketingConsent,
                        isDark: themeManager.isDarkMode
                    )
                    .onChange(of: complianceManager.marketingConsent) { consent in
                        complianceManager.setMarketingConsent(consent)
                    }
                    
                    // Continue Button
                    PrimaryButton(
                        title: "המשך",
                        isEnabled: complianceManager.hasRequiredConsents,
                        isDark: themeManager.isDarkMode
                    ) {
                        dismiss()
                    }
                    .padding(.top, DesignSystem.Spacing.lg.rawValue)
                }
                .padding(DesignSystem.Spacing.lg.rawValue)
            }
            .navigationTitle("פרטיות")
            .navigationBarTitleDisplayMode(.inline)
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $showingPrivacyPolicy) {
            WebDocumentView(
                title: "מדיניות פרטיות",
                content: privacyPolicyContent
            )
        }
        .sheet(isPresented: $showingTermsOfService) {
            WebDocumentView(
                title: "תנאי שימוש",
                content: termsOfServiceContent
            )
        }
    }
    
    private var privacyPolicyContent: String {
        return """
        # מדיניות פרטיות - מערכת חשבוניות
        
        ## איסוף מידע
        אנו אוספים מידע שאתה מספק לנו ישירות, כגון:
        - פרטי החברה והחשבון
        - מסמכים וחשבוניות שאתה יוצר
        - נתוני שימוש באפליקציה
        
        ## שימוש במידע
        אנו משתמשים במידע שלך כדי:
        - לספק את שירותי האפליקציה
        - לשפר את החוויה שלך
        - לתמוך בך בעת הצורך
        
        ## אבטחת מידע
        אנו נוקטים באמצעי אבטחה מתקדמים כדי להגן על המידע שלך.
        
        ## זכויותיך
        יש לך זכות לגשת למידע שלך, לעדכן אותו או למחוק אותו.
        
        לפרטים נוספים, צור קשר: <EMAIL>
        """
    }
    
    private var termsOfServiceContent: String {
        return """
        # תנאי שימוש - מערכת חשבוניות
        
        ## קבלת התנאים
        השימוש באפליקציה מהווה הסכמה לתנאים אלה.
        
        ## השירות
        האפליקציה מספקת כלים לניהול חשבוניות והוצאות עסקיות.
        
        ## אחריות המשתמש
        אתה אחראי לדיוק המידע שאתה מזין ולשמירה על אבטחת החשבון.
        
        ## הגבלת אחריות
        השירות ניתן "כפי שהוא" ואיננו מתחייבים לזמינות מלאה.
        
        ## שינויים
        אנו רשאים לעדכן תנאים אלה מעת לעת.
        
        לפרטים נוספים, צור קשר: <EMAIL>
        """
    }
}

struct WebDocumentView: View {
    let title: String
    let content: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                Text(content)
                    .font(.hebrewFont(size: .base))
                    .padding(DesignSystem.Spacing.lg.rawValue)
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("סגור") {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - App Store Metadata

struct AppStoreMetadata {
    static let appName = "מערכת חשבוניות"
    static let appDescription = """
    מערכת ניהול חשבוניות והוצאות מתקדמת לעסקים קטנים ובינוניים.
    
    תכונות עיקריות:
    • יצירת חשבוניות מס וקבלות
    • ניהול לקוחות וספקים
    • סריקת הוצאות באמצעות המצלמה
    • דוחות מע״מ אוטומטיים
    • סנכרון עם מערכות הנהלת חשבונות
    • תמיכה מלאה בעברית ו-RTL
    
    האפליקציה מיועדת לעסקים הרוצים לייעל את תהליכי החשבונאות ולחסוך זמן יקר.
    """
    
    static let keywords = [
        "חשבוניות", "הוצאות", "עסק", "מע״מ", "קבלות", "חשבונאות",
        "invoices", "expenses", "business", "VAT", "receipts", "accounting"
    ]
    
    static let supportURL = "https://invoiceapp.com/support"
    static let privacyPolicyURL = "https://invoiceapp.com/privacy"
    static let termsOfServiceURL = "https://invoiceapp.com/terms"
    
    static let contentRating = "4+" // Suitable for all ages
    static let category = "Business"
    static let subcategory = "Finance"
}

// MARK: - Crash Reporting (Privacy-Safe)

class CrashReporter {
    static let shared = CrashReporter()
    
    private init() {
        setupCrashHandler()
    }
    
    private func setupCrashHandler() {
        NSSetUncaughtExceptionHandler { exception in
            CrashReporter.shared.logCrash(exception: exception)
        }
        
        signal(SIGABRT) { _ in
            CrashReporter.shared.logSignal("SIGABRT")
        }
        
        signal(SIGILL) { _ in
            CrashReporter.shared.logSignal("SIGILL")
        }
        
        signal(SIGSEGV) { _ in
            CrashReporter.shared.logSignal("SIGSEGV")
        }
        
        signal(SIGFPE) { _ in
            CrashReporter.shared.logSignal("SIGFPE")
        }
        
        signal(SIGBUS) { _ in
            CrashReporter.shared.logSignal("SIGBUS")
        }
        
        signal(SIGPIPE) { _ in
            CrashReporter.shared.logSignal("SIGPIPE")
        }
    }
    
    private func logCrash(exception: NSException) {
        let crashReport = [
            "timestamp": ISO8601DateFormatter().string(from: Date()),
            "app_version": EnvironmentManager.shared.appVersion,
            "build_number": EnvironmentManager.shared.buildNumber,
            "device_model": DeviceInfo.modelName,
            "system_version": DeviceInfo.systemVersion,
            "exception_name": exception.name.rawValue,
            "exception_reason": exception.reason ?? "Unknown",
            "call_stack": exception.callStackSymbols
        ] as [String: Any]
        
        // Store crash report locally (would be sent to crash reporting service in production)
        storeCrashReport(crashReport)
        
        Logger.shared.critical("App crashed: \(exception.name.rawValue) - \(exception.reason ?? "Unknown")")
    }
    
    private func logSignal(_ signal: String) {
        let crashReport = [
            "timestamp": ISO8601DateFormatter().string(from: Date()),
            "app_version": EnvironmentManager.shared.appVersion,
            "build_number": EnvironmentManager.shared.buildNumber,
            "device_model": DeviceInfo.modelName,
            "system_version": DeviceInfo.systemVersion,
            "signal": signal
        ] as [String: Any]
        
        storeCrashReport(crashReport)
        
        Logger.shared.critical("App received signal: \(signal)")
    }
    
    private func storeCrashReport(_ report: [String: Any]) {
        // In a production app, this would send the crash report to a service like Crashlytics
        // For privacy compliance, ensure no personal data is included
        
        if let data = try? JSONSerialization.data(withJSONObject: report, options: .prettyPrinted) {
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let crashReportPath = documentsPath.appendingPathComponent("crash_reports")
            
            try? FileManager.default.createDirectory(at: crashReportPath, withIntermediateDirectories: true)
            
            let fileName = "crash_\(Date().timeIntervalSince1970).json"
            let filePath = crashReportPath.appendingPathComponent(fileName)
            
            try? data.write(to: filePath)
        }
    }
}
