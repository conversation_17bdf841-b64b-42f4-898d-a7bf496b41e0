//
//  ThemeManager.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import Combine

class ThemeManager: ObservableObject {
    @Published var isDarkMode: Bool = true
    
    var colorScheme: ColorScheme {
        isDarkMode ? .dark : .light
    }
    
    init() {
        // Load saved theme preference
        self.isDarkMode = UserDefaults.standard.bool(forKey: "isDarkMode")
    }
    
    func toggleTheme() {
        isDarkMode.toggle()
        UserDefaults.standard.set(isDarkMode, forKey: "isDarkMode")
    }
}

// MARK: - Design System Colors
extension Color {
    // Core Palette - Dark Theme (Default)
    static let backgroundPrimary = Color(red: 0.08, green: 0.08, blue: 0.08) // #141414
    static let foregroundPrimary = Color(red: 0.95, green: 0.95, blue: 0.95) // #F2F2F2
    static let cardBackground = Color(red: 0.12, green: 0.12, blue: 0.12) // #1F1F1F
    static let primaryAccent = Color(red: 0.85, green: 0.85, blue: 0.85) // #D9D9D9
    static let secondaryBackground = Color(red: 0.18, green: 0.18, blue: 0.18) // #2E2E2E
    static let mutedBackground = Color(red: 0.22, green: 0.22, blue: 0.22) // #383838
    static let mutedForeground = Color(red: 0.70, green: 0.70, blue: 0.70) // #B3B3B3
    static let borderColor = Color(red: 0.22, green: 0.22, blue: 0.22) // #383838
    
    // Light Theme Colors
    static let backgroundLight = Color(red: 0.98, green: 0.98, blue: 0.98) // #FAFAFA
    static let foregroundLight = Color(red: 0.20, green: 0.20, blue: 0.20) // #333333
    static let cardLight = Color(red: 0.96, green: 0.96, blue: 0.96) // #F5F5F5
    static let primaryLight = Color(red: 0.25, green: 0.25, blue: 0.25) // #404040
    static let secondaryLight = Color(red: 0.92, green: 0.92, blue: 0.92) // #EBEBEB
    static let mutedLight = Color(red: 0.88, green: 0.88, blue: 0.88) // #E0E0E0
    static let borderLight = Color(red: 0.85, green: 0.85, blue: 0.85) // #D9D9D9
    
    // Cosmic Accent Colors
    static let cosmicDark = Color(red: 0.25, green: 0.25, blue: 0.25) // #404040
    static let cosmicAccent = Color(red: 0.38, green: 0.38, blue: 0.38) // #606060
    static let cosmicMuted = Color(red: 0.56, green: 0.56, blue: 0.56) // #909090
    
    // State Colors
    static let destructive = Color(red: 0.96, green: 0.40, blue: 0.40) // #F56565
    static let success = Color(red: 0.34, green: 0.80, blue: 0.61) // #56CC9D
    static let warning = Color(red: 0.98, green: 0.73, blue: 0.20) // #FAB833
    
    // Dynamic Colors based on theme
    static func dynamicBackground(_ isDark: Bool) -> Color {
        isDark ? backgroundPrimary : backgroundLight
    }
    
    static func dynamicForeground(_ isDark: Bool) -> Color {
        isDark ? foregroundPrimary : foregroundLight
    }
    
    static func dynamicCard(_ isDark: Bool) -> Color {
        isDark ? cardBackground : cardLight
    }
    
    static func dynamicPrimary(_ isDark: Bool) -> Color {
        isDark ? primaryAccent : primaryLight
    }
    
    static func dynamicSecondary(_ isDark: Bool) -> Color {
        isDark ? secondaryBackground : secondaryLight
    }
    
    static func dynamicMuted(_ isDark: Bool) -> Color {
        isDark ? mutedBackground : mutedLight
    }
    
    static func dynamicBorder(_ isDark: Bool) -> Color {
        isDark ? borderColor : borderLight
    }
}

// MARK: - Typography
struct DesignSystem {
    // Font Families
    static let hebrewFont = "SecularOne-Regular"
    static let latinFont = "Inter"
    
    // Font Sizes
    enum FontSize: CGFloat {
        case micro = 12      // .text-xs
        case small = 14      // .text-sm
        case base = 16       // .text-base
        case large = 18      // .text-lg
        case xl = 20         // .text-xl
        case xxl = 24        // .text-2xl
        case xxxl = 30       // .text-3xl
        case hero = 36       // .text-4xl
    }
    
    // Spacing
    enum Spacing: CGFloat {
        case xs = 4          // 1
        case sm = 8          // 2
        case md = 12         // 3
        case base = 16       // 4
        case lg = 20         // 5
        case xl = 24         // 6
        case xxl = 32        // 8
        case xxxl = 40       // 10
        case huge = 48       // 12
        case massive = 64    // 16
        case giant = 80      // 20
        case colossal = 96   // 24
    }
    
    // Border Radius
    enum Radius: CGFloat {
        case small = 6       // .rounded-md
        case base = 8        // .rounded-lg
        case large = 12      // .rounded-xl
        case xlarge = 16     // .rounded-2xl
        case full = 9999     // .rounded-full
    }
}

// MARK: - Custom Font Extensions
extension Font {
    static func hebrewFont(size: DesignSystem.FontSize, weight: Font.Weight = .regular) -> Font {
        return Font.custom(DesignSystem.hebrewFont, size: size.rawValue).weight(weight)
    }
    
    static func latinFont(size: DesignSystem.FontSize, weight: Font.Weight = .regular) -> Font {
        return Font.custom(DesignSystem.latinFont, size: size.rawValue).weight(weight)
    }
}

// MARK: - View Modifiers
struct CosmicGlowModifier: ViewModifier {
    let isDark: Bool
    
    func body(content: Content) -> some View {
        content
            .shadow(
                color: isDark ? Color.white.opacity(0.2) : Color.black.opacity(0.1),
                radius: 20,
                x: 0,
                y: 0
            )
    }
}

struct CardStyleModifier: ViewModifier {
    let isDark: Bool
    
    func body(content: Content) -> some View {
        content
            .background(Color.dynamicCard(isDark))
            .cornerRadius(DesignSystem.Radius.base.rawValue)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                    .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
            )
    }
}

extension View {
    func cosmicGlow(isDark: Bool = true) -> some View {
        self.modifier(CosmicGlowModifier(isDark: isDark))
    }
    
    func cardStyle(isDark: Bool = true) -> some View {
        self.modifier(CardStyleModifier(isDark: isDark))
    }
}
