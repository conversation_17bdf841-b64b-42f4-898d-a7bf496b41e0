//
//  DesignComponents.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

// MARK: - Reusable UI Components

struct PrimaryButton: View {
    let title: String
    let action: () -> Void
    let isLoading: Bool
    let isEnabled: Bool
    let isDark: Bool
    
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        isDark: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.isDark = isDark
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: isDark ? Color.backgroundPrimary : Color.foregroundLight))
                        .scaleEffect(0.8)
                }
                
                Text(title)
                    .font(.hebrewFont(size: .large, weight: .semibold))
                    .foregroundColor(isDark ? Color.backgroundPrimary : Color.foregroundLight)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(Color.dynamicPrimary(isDark))
            .cornerRadius(DesignSystem.Radius.base.rawValue)
            .opacity(isEnabled ? 1.0 : 0.6)
            .cosmicGlow(isDark: isDark)
        }
        .disabled(!isEnabled || isLoading)
    }
}

struct SecondaryButton: View {
    let title: String
    let action: () -> Void
    let isEnabled: Bool
    let isDark: Bool
    
    init(
        title: String,
        isEnabled: Bool = true,
        isDark: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isEnabled = isEnabled
        self.isDark = isDark
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.hebrewFont(size: .large, weight: .semibold))
                .foregroundColor(Color.dynamicForeground(isDark))
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(Color.clear)
                .cornerRadius(DesignSystem.Radius.base.rawValue)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                        .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
                )
                .opacity(isEnabled ? 1.0 : 0.6)
        }
        .disabled(!isEnabled)
    }
}

struct IconButton: View {
    let icon: String
    let action: () -> Void
    let size: CGFloat
    let color: Color
    let backgroundColor: Color?
    
    init(
        icon: String,
        size: CGFloat = 24,
        color: Color = .primary,
        backgroundColor: Color? = nil,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.size = size
        self.color = color
        self.backgroundColor = backgroundColor
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size))
                .foregroundColor(color)
                .frame(width: size + 16, height: size + 16)
                .background(backgroundColor)
                .cornerRadius(DesignSystem.Radius.base.rawValue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct InputField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let isDark: Bool
    let validation: ValidationResult?
    
    @State private var showPassword = false
    
    init(
        title: String,
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        isDark: Bool = true,
        validation: ValidationResult? = nil
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.isDark = isDark
        self.validation = validation
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            Text(title)
                .font(.hebrewFont(size: .base, weight: .medium))
                .foregroundColor(Color.dynamicForeground(isDark))
            
            HStack {
                if isSecure {
                    Button(action: { showPassword.toggle() }) {
                        Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                            .foregroundColor(Color.mutedForeground)
                    }
                }
                
                if isSecure && !showPassword {
                    SecureField(placeholder, text: $text)
                        .font(.hebrewFont(size: .base))
                        .multilineTextAlignment(.trailing)
                } else {
                    TextField(placeholder, text: $text)
                        .font(.hebrewFont(size: .base))
                        .keyboardType(keyboardType)
                        .multilineTextAlignment(.trailing)
                }
            }
            .padding(DesignSystem.Spacing.base.rawValue)
            .background(Color.dynamicCard(isDark))
            .cornerRadius(DesignSystem.Radius.base.rawValue)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                    .stroke(borderColor, lineWidth: 1)
            )
            
            if let validation = validation, !validation.isValid {
                VStack(alignment: .trailing, spacing: 2) {
                    ForEach(validation.errors, id: \.self) { error in
                        Text(error)
                            .font(.hebrewFont(size: .small))
                            .foregroundColor(Color.destructive)
                    }
                }
            }
        }
    }
    
    private var borderColor: Color {
        if let validation = validation, !validation.isValid {
            return Color.destructive
        }
        return Color.dynamicBorder(isDark)
    }
}

struct PickerField<T: Hashable>: View {
    let title: String
    @Binding var selection: T?
    let options: [T]
    let displayName: (T) -> String
    let placeholder: String
    let isDark: Bool
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm.rawValue) {
            Text(title)
                .font(.hebrewFont(size: .base, weight: .medium))
                .foregroundColor(Color.dynamicForeground(isDark))
            
            Menu {
                ForEach(options, id: \.self) { option in
                    Button(displayName(option)) {
                        selection = option
                    }
                }
            } label: {
                HStack {
                    Image(systemName: "chevron.down")
                        .foregroundColor(Color.mutedForeground)
                    
                    Spacer()
                    
                    Text(selection.map(displayName) ?? placeholder)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(selection == nil ? Color.mutedForeground : Color.dynamicForeground(isDark))
                }
                .padding(DesignSystem.Spacing.base.rawValue)
                .background(Color.dynamicCard(isDark))
                .cornerRadius(DesignSystem.Radius.base.rawValue)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Radius.base.rawValue)
                        .stroke(Color.dynamicBorder(isDark), lineWidth: 1)
                )
            }
        }
    }
}

struct ToggleField: View {
    let title: String
    let subtitle: String?
    @Binding var isOn: Bool
    let isDark: Bool
    
    var body: some View {
        HStack {
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.dynamicPrimary(isDark)))
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(title)
                    .font(.hebrewFont(size: .base, weight: .medium))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.hebrewFont(size: .small))
                        .foregroundColor(Color.mutedForeground)
                }
            }
        }
        .padding(DesignSystem.Spacing.base.rawValue)
        .cardStyle(isDark: isDark)
    }
}

struct StatusBadge: View {
    let text: String
    let color: Color
    let size: BadgeSize
    
    enum BadgeSize {
        case small, medium, large
        
        var fontSize: DesignSystem.FontSize {
            switch self {
            case .small: return .micro
            case .medium: return .small
            case .large: return .base
            }
        }
        
        var padding: CGFloat {
            switch self {
            case .small: return 4
            case .medium: return 6
            case .large: return 8
            }
        }
    }
    
    var body: some View {
        Text(text)
            .font(.hebrewFont(size: size.fontSize, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, size.padding + 2)
            .padding(.vertical, size.padding)
            .background(color)
            .cornerRadius(DesignSystem.Radius.small.rawValue)
    }
}

struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionTitle: String?
    let action: (() -> Void)?
    let isDark: Bool
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: icon)
                    .font(.system(size: 60))
                    .foregroundColor(Color.mutedForeground)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text(title)
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                        .multilineTextAlignment(.center)
                    
                    Text(subtitle)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
            }
            
            if let actionTitle = actionTitle, let action = action {
                PrimaryButton(
                    title: actionTitle,
                    isDark: isDark,
                    action: action
                )
                .frame(maxWidth: 200)
            }
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
}

struct LoadingView: View {
    let message: String?
    let isDark: Bool
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color.dynamicPrimary(isDark)))
                .scaleEffect(1.5)
            
            if let message = message {
                Text(message)
                    .font(.hebrewFont(size: .base))
                    .foregroundColor(Color.mutedForeground)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.dynamicBackground(isDark).opacity(0.8))
    }
}

struct ErrorView: View {
    let title: String
    let message: String
    let retryAction: (() -> Void)?
    let isDark: Bool
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl.rawValue) {
            VStack(spacing: DesignSystem.Spacing.lg.rawValue) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(Color.destructive)
                
                VStack(spacing: DesignSystem.Spacing.sm.rawValue) {
                    Text(title)
                        .font(.hebrewFont(size: .xl, weight: .semibold))
                        .foregroundColor(Color.dynamicForeground(isDark))
                        .multilineTextAlignment(.center)
                    
                    Text(message)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                        .multilineTextAlignment(.center)
                }
            }
            
            if let retryAction = retryAction {
                PrimaryButton(
                    title: "נסה שוב",
                    isDark: isDark,
                    action: retryAction
                )
                .frame(maxWidth: 200)
            }
        }
        .padding(DesignSystem.Spacing.xl.rawValue)
    }
}

struct SectionHeader: View {
    let title: String
    let subtitle: String?
    let action: (() -> Void)?
    let actionTitle: String?
    let isDark: Bool
    
    var body: some View {
        HStack {
            if let action = action, let actionTitle = actionTitle {
                Button(action: action) {
                    Text(actionTitle)
                        .font(.hebrewFont(size: .base, weight: .medium))
                        .foregroundColor(Color.dynamicPrimary(isDark))
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(title)
                    .font(.hebrewFont(size: .xl, weight: .semibold))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.hebrewFont(size: .base))
                        .foregroundColor(Color.mutedForeground)
                }
            }
        }
    }
}

struct ProgressBar: View {
    let progress: Double
    let total: Double
    let color: Color
    let isDark: Bool
    
    private var percentage: Double {
        guard total > 0 else { return 0 }
        return min(progress / total, 1.0)
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs.rawValue) {
            HStack {
                Text("\(Int(percentage * 100))%")
                    .font(.hebrewFont(size: .small, weight: .medium))
                    .foregroundColor(Color.dynamicForeground(isDark))
                
                Spacer()
                
                Text("\(Int(progress)) מתוך \(Int(total))")
                    .font(.hebrewFont(size: .small))
                    .foregroundColor(Color.mutedForeground)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .trailing) {
                    Rectangle()
                        .fill(Color.dynamicMuted(isDark))
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * percentage, height: 8)
                        .cornerRadius(4)
                        .animation(.easeInOut(duration: 0.3), value: percentage)
                }
            }
            .frame(height: 8)
        }
    }
}

// MARK: - Animation Helpers

struct SlideTransition: ViewModifier {
    let isVisible: Bool
    let direction: SlideDirection
    
    enum SlideDirection {
        case leading, trailing, top, bottom
    }
    
    func body(content: Content) -> some View {
        content
            .offset(offset)
            .opacity(isVisible ? 1 : 0)
            .animation(.easeInOut(duration: 0.3), value: isVisible)
    }
    
    private var offset: CGSize {
        guard !isVisible else { return .zero }
        
        switch direction {
        case .leading:
            return CGSize(width: -100, height: 0)
        case .trailing:
            return CGSize(width: 100, height: 0)
        case .top:
            return CGSize(width: 0, height: -100)
        case .bottom:
            return CGSize(width: 0, height: 100)
        }
    }
}

extension View {
    func slideTransition(isVisible: Bool, direction: SlideTransition.SlideDirection) -> some View {
        self.modifier(SlideTransition(isVisible: isVisible, direction: direction))
    }
}

// MARK: - Haptic Feedback

class HapticManager {
    static let shared = HapticManager()
    
    private init() {}
    
    func impact(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        guard EnvironmentManager.shared.hapticFeedbackEnabled else { return }
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        guard EnvironmentManager.shared.hapticFeedbackEnabled else { return }
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }
    
    func selection() {
        guard EnvironmentManager.shared.hapticFeedbackEnabled else { return }
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
    }
}
