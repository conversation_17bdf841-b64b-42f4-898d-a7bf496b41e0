//
//  ValidationHelpers.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation

// MARK: - Validation Helpers

struct Validator {
    
    // MARK: - Email Validation
    
    static func validateEmail(_ email: String) -> ValidationResult {
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedEmail.isEmpty else {
            return .invalid("כתובת אימייל נדרשת")
        }
        
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        guard emailPredicate.evaluate(with: trimmedEmail) else {
            return .invalid("כתובת אימייל לא תקינה")
        }
        
        return .valid
    }
    
    // MARK: - Password Validation
    
    static func validatePassword(_ password: String) -> ValidationResult {
        var errors: [String] = []
        
        if password.isEmpty {
            errors.append("סיסמה נדרשת")
        } else {
            if password.count < 8 {
                errors.append("הסיסמה חייבת להכיל לפחות 8 תווים")
            }
            
            if !password.contains(where: { $0.isUppercase }) {
                errors.append("הסיסמה חייבת להכיל לפחות אות גדולה אחת")
            }
            
            if !password.contains(where: { $0.isLowercase }) {
                errors.append("הסיסמה חייבת להכיל לפחות אות קטנה אחת")
            }
            
            if !password.contains(where: { $0.isNumber }) {
                errors.append("הסיסמה חייבת להכיל לפחות ספרה אחת")
            }
            
            let specialCharacters = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if !password.contains(where: { specialCharacters.contains($0) }) {
                errors.append("הסיסמה חייבת להכיל לפחות תו מיוחד אחד")
            }
        }
        
        return errors.isEmpty ? .valid : .invalid(errors)
    }
    
    // MARK: - Phone Validation
    
    static func validatePhone(_ phone: String) -> ValidationResult {
        let trimmedPhone = phone.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedPhone.isEmpty else {
            return .invalid("מספר טלפון נדרש")
        }
        
        // Remove common formatting characters
        let cleanPhone = trimmedPhone.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        
        // Israeli phone number validation
        let phoneRegex = "^(05[0-9]|02|03|04|08|09)[0-9]{7}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        
        guard phonePredicate.evaluate(with: cleanPhone) else {
            return .invalid("מספר טלפון לא תקין")
        }
        
        return .valid
    }
    
    // MARK: - Business Number Validation
    
    static func validateBusinessNumber(_ businessNumber: String) -> ValidationResult {
        let trimmedNumber = businessNumber.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedNumber.isEmpty else {
            return .invalid("מספר עסק נדרש")
        }
        
        // Remove formatting characters
        let cleanNumber = trimmedNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        
        guard cleanNumber.count == 9 else {
            return .invalid("מספר עסק חייב להכיל 9 ספרות")
        }
        
        // Israeli business number checksum validation
        guard isValidIsraeliBusinessNumber(cleanNumber) else {
            return .invalid("מספר עסק לא תקין")
        }
        
        return .valid
    }
    
    private static func isValidIsraeliBusinessNumber(_ number: String) -> Bool {
        guard number.count == 9 else { return false }
        
        let digits = number.compactMap { Int(String($0)) }
        guard digits.count == 9 else { return false }
        
        var sum = 0
        for (index, digit) in digits.enumerated() {
            let multiplier = (index % 2) + 1
            let product = digit * multiplier
            sum += product > 9 ? product - 9 : product
        }
        
        return sum % 10 == 0
    }
    
    // MARK: - Name Validation
    
    static func validateName(_ name: String) -> ValidationResult {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty else {
            return .invalid("שם נדרש")
        }
        
        guard trimmedName.count >= 2 else {
            return .invalid("השם חייב להכיל לפחות 2 תווים")
        }
        
        guard trimmedName.count <= 50 else {
            return .invalid("השם לא יכול להכיל יותר מ-50 תווים")
        }
        
        // Check for valid characters (Hebrew, English, spaces, hyphens, apostrophes)
        let nameRegex = "^[א-ת\\u05D0-\\u05EAa-zA-Z\\s'-]+$"
        let namePredicate = NSPredicate(format: "SELF MATCHES %@", nameRegex)
        
        guard namePredicate.evaluate(with: trimmedName) else {
            return .invalid("השם מכיל תווים לא חוקיים")
        }
        
        return .valid
    }
    
    // MARK: - Address Validation
    
    static func validateAddress(_ address: String) -> ValidationResult {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedAddress.isEmpty else {
            return .invalid("כתובת נדרשת")
        }
        
        guard trimmedAddress.count >= 5 else {
            return .invalid("הכתובת חייבת להכיל לפחות 5 תווים")
        }
        
        guard trimmedAddress.count <= 100 else {
            return .invalid("הכתובת לא יכולה להכיל יותר מ-100 תווים")
        }
        
        return .valid
    }
    
    // MARK: - City Validation
    
    static func validateCity(_ city: String) -> ValidationResult {
        let trimmedCity = city.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedCity.isEmpty else {
            return .invalid("עיר נדרשת")
        }
        
        guard trimmedCity.count >= 2 else {
            return .invalid("שם העיר חייב להכיל לפחות 2 תווים")
        }
        
        guard trimmedCity.count <= 30 else {
            return .invalid("שם העיר לא יכול להכיל יותר מ-30 תווים")
        }
        
        // Check for valid characters (Hebrew, English, spaces, hyphens)
        let cityRegex = "^[א-ת\\u05D0-\\u05EAa-zA-Z\\s'-]+$"
        let cityPredicate = NSPredicate(format: "SELF MATCHES %@", cityRegex)
        
        guard cityPredicate.evaluate(with: trimmedCity) else {
            return .invalid("שם העיר מכיל תווים לא חוקיים")
        }
        
        return .valid
    }
    
    // MARK: - Industry Validation
    
    static func validateIndustry(_ industry: String) -> ValidationResult {
        guard !industry.isEmpty else {
            return .invalid("תחום עיסוק נדרש")
        }
        
        let validIndustries = [
            "טכנולוגיה", "קמעונאות", "שירותים", "ייצור", 
            "בנייה", "בריאות", "חינוך", "תחבורה", "אחר"
        ]
        
        guard validIndustries.contains(industry) else {
            return .invalid("תחום עיסוק לא תקין")
        }
        
        return .valid
    }
    
    // MARK: - Combined Validation
    
    static func validateRegistrationStep1(industry: String, annualRevenue: String?) -> ValidationResult {
        let industryValidation = validateIndustry(industry)
        
        if !industryValidation.isValid {
            return industryValidation
        }
        
        // Annual revenue is optional, but if provided, validate it
        if let revenue = annualRevenue, !revenue.isEmpty {
            let validRevenues = [
                "פחות מ-500 אלף", "500 אלף - מיליון", "מיליון - 5 מיליון", 
                "5-10 מיליון", "מעל 10 מיליון"
            ]
            
            guard validRevenues.contains(revenue) else {
                return .invalid("מחזור שנתי לא תקין")
            }
        }
        
        return .valid
    }
    
    static func validateRegistrationStep2(
        companyId: String,
        nameHebrew: String,
        addressHebrew: String,
        cityHebrew: String,
        businessPhone: String
    ) -> ValidationResult {
        let validations = [
            validateBusinessNumber(companyId),
            validateName(nameHebrew),
            validateAddress(addressHebrew),
            validateCity(cityHebrew),
            validatePhone(businessPhone)
        ]
        
        let errors = validations.flatMap { $0.errors }
        return errors.isEmpty ? .valid : .invalid(errors)
    }
    
    static func validateRegistrationStep3(
        fullName: String,
        email: String,
        phone: String,
        password: String
    ) -> ValidationResult {
        let validations = [
            validateName(fullName),
            validateEmail(email),
            validatePhone(phone),
            validatePassword(password)
        ]
        
        let errors = validations.flatMap { $0.errors }
        return errors.isEmpty ? .valid : .invalid(errors)
    }
    
    static func validateLoginForm(email: String, password: String) -> ValidationResult {
        var errors: [String] = []
        
        let emailValidation = validateEmail(email)
        if !emailValidation.isValid {
            errors.append(contentsOf: emailValidation.errors)
        }
        
        if password.isEmpty {
            errors.append("סיסמה נדרשת")
        }
        
        return errors.isEmpty ? .valid : .invalid(errors)
    }
}

// MARK: - Form State Management

class FormStateManager: ObservableObject {
    @Published var validationResults: [String: ValidationResult] = [:]
    @Published var isFormValid = false
    
    func validate(field: String, value: String, validator: (String) -> ValidationResult) {
        let result = validator(value)
        validationResults[field] = result
        updateFormValidity()
    }
    
    func validateMultiple(validations: [(String, ValidationResult)]) {
        for (field, result) in validations {
            validationResults[field] = result
        }
        updateFormValidity()
    }
    
    func clearValidation(for field: String) {
        validationResults.removeValue(forKey: field)
        updateFormValidity()
    }
    
    func clearAllValidations() {
        validationResults.removeAll()
        updateFormValidity()
    }
    
    private func updateFormValidity() {
        isFormValid = validationResults.values.allSatisfy { $0.isValid }
    }
    
    func getValidationResult(for field: String) -> ValidationResult? {
        return validationResults[field]
    }
    
    func hasErrors(for field: String) -> Bool {
        return validationResults[field]?.isValid == false
    }
}

// MARK: - Input Formatters

struct InputFormatter {
    
    static func formatPhone(_ input: String) -> String {
        let digits = input.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        
        guard !digits.isEmpty else { return "" }
        
        if digits.hasPrefix("972") {
            // International format
            let formatted = String(digits.dropFirst(3))
            return formatIsraeliPhone(formatted)
        } else {
            return formatIsraeliPhone(digits)
        }
    }
    
    private static func formatIsraeliPhone(_ digits: String) -> String {
        guard digits.count >= 3 else { return digits }
        
        if digits.hasPrefix("05") {
            // Mobile number: 05X-XXX-XXXX
            let prefix = String(digits.prefix(3))
            let middle = String(digits.dropFirst(3).prefix(3))
            let suffix = String(digits.dropFirst(6))
            
            if digits.count <= 3 {
                return prefix
            } else if digits.count <= 6 {
                return "\(prefix)-\(middle)"
            } else {
                return "\(prefix)-\(middle)-\(suffix)"
            }
        } else {
            // Landline: 0X-XXX-XXXX
            let prefix = String(digits.prefix(2))
            let middle = String(digits.dropFirst(2).prefix(3))
            let suffix = String(digits.dropFirst(5))
            
            if digits.count <= 2 {
                return prefix
            } else if digits.count <= 5 {
                return "\(prefix)-\(middle)"
            } else {
                return "\(prefix)-\(middle)-\(suffix)"
            }
        }
    }
    
    static func formatBusinessNumber(_ input: String) -> String {
        let digits = input.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        
        guard !digits.isEmpty else { return "" }
        guard digits.count <= 9 else { return String(digits.prefix(9)) }
        
        if digits.count <= 3 {
            return digits
        } else if digits.count <= 6 {
            return "\(digits.prefix(3))-\(digits.dropFirst(3))"
        } else {
            return "\(digits.prefix(3))-\(digits.dropFirst(3).prefix(3))-\(digits.dropFirst(6))"
        }
    }
    
    static func formatCurrency(_ amount: Double, currency: Currency = .ils) -> String {
        let formatter = NumberFormatter.currency(for: currency)
        return formatter.string(from: NSNumber(value: amount)) ?? "\(currency.symbol)0"
    }
    
    static func formatDate(_ date: Date, format: DateFormat = .ddmmyyyy) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = format.rawValue
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
    
    static func formatPercentage(_ value: Double) -> String {
        let formatter = NumberFormatter.percentage()
        return formatter.string(from: NSNumber(value: value)) ?? "0%"
    }
}
