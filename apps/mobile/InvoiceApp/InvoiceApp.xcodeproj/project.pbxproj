// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		CE94DC1A2E62CABF00C0649F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CE94DC042E62CABD00C0649F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CE94DC0B2E62CABD00C0649F;
			remoteInfo = InvoiceApp;
		};
		CE94DC242E62CABF00C0649F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CE94DC042E62CABD00C0649F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CE94DC0B2E62CABD00C0649F;
			remoteInfo = InvoiceApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		CE94DC0C2E62CABD00C0649F /* InvoiceApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = InvoiceApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CE94DC192E62CABF00C0649F /* InvoiceAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = InvoiceAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		CE94DC232E62CABF00C0649F /* InvoiceAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = InvoiceAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		CE94DC0E2E62CABD00C0649F /* InvoiceApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceApp;
			sourceTree = "<group>";
		};
		CE94DC1C2E62CABF00C0649F /* InvoiceAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceAppTests;
			sourceTree = "<group>";
		};
		CE94DC262E62CABF00C0649F /* InvoiceAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = InvoiceAppUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		CE94DC092E62CABD00C0649F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC162E62CABF00C0649F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC202E62CABF00C0649F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CE94DC032E62CABD00C0649F = {
			isa = PBXGroup;
			children = (
				CE94DC372E62CB6600C0649F /* New Group */,
				CE94DC1C2E62CABF00C0649F /* InvoiceAppTests */,
				CE94DC262E62CABF00C0649F /* InvoiceAppUITests */,
				CE94DC0D2E62CABD00C0649F /* Products */,
			);
			sourceTree = "<group>";
		};
		CE94DC0D2E62CABD00C0649F /* Products */ = {
			isa = PBXGroup;
			children = (
				CE94DC0C2E62CABD00C0649F /* InvoiceApp.app */,
				CE94DC192E62CABF00C0649F /* InvoiceAppTests.xctest */,
				CE94DC232E62CABF00C0649F /* InvoiceAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CE94DC372E62CB6600C0649F /* New Group */ = {
			isa = PBXGroup;
			children = (
				CE94DC382E62CB7800C0649F /* New Group */,
			);
			path = "New Group";
			sourceTree = "<group>";
		};
		CE94DC382E62CB7800C0649F /* New Group */ = {
			isa = PBXGroup;
			children = (
				CE94DC0E2E62CABD00C0649F /* InvoiceApp */,
			);
			path = "New Group";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CE94DC0B2E62CABD00C0649F /* InvoiceApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE94DC2D2E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceApp" */;
			buildPhases = (
				CE94DC082E62CABD00C0649F /* Sources */,
				CE94DC092E62CABD00C0649F /* Frameworks */,
				CE94DC0A2E62CABD00C0649F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				CE94DC0E2E62CABD00C0649F /* InvoiceApp */,
			);
			name = InvoiceApp;
			packageProductDependencies = (
			);
			productName = InvoiceApp;
			productReference = CE94DC0C2E62CABD00C0649F /* InvoiceApp.app */;
			productType = "com.apple.product-type.application";
		};
		CE94DC182E62CABF00C0649F /* InvoiceAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE94DC302E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceAppTests" */;
			buildPhases = (
				CE94DC152E62CABF00C0649F /* Sources */,
				CE94DC162E62CABF00C0649F /* Frameworks */,
				CE94DC172E62CABF00C0649F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CE94DC1B2E62CABF00C0649F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CE94DC1C2E62CABF00C0649F /* InvoiceAppTests */,
			);
			name = InvoiceAppTests;
			packageProductDependencies = (
			);
			productName = InvoiceAppTests;
			productReference = CE94DC192E62CABF00C0649F /* InvoiceAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		CE94DC222E62CABF00C0649F /* InvoiceAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE94DC332E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceAppUITests" */;
			buildPhases = (
				CE94DC1F2E62CABF00C0649F /* Sources */,
				CE94DC202E62CABF00C0649F /* Frameworks */,
				CE94DC212E62CABF00C0649F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CE94DC252E62CABF00C0649F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CE94DC262E62CABF00C0649F /* InvoiceAppUITests */,
			);
			name = InvoiceAppUITests;
			packageProductDependencies = (
			);
			productName = InvoiceAppUITests;
			productReference = CE94DC232E62CABF00C0649F /* InvoiceAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CE94DC042E62CABD00C0649F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					CE94DC0B2E62CABD00C0649F = {
						CreatedOnToolsVersion = 16.4;
					};
					CE94DC182E62CABF00C0649F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = CE94DC0B2E62CABD00C0649F;
					};
					CE94DC222E62CABF00C0649F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = CE94DC0B2E62CABD00C0649F;
					};
				};
			};
			buildConfigurationList = CE94DC072E62CABD00C0649F /* Build configuration list for PBXProject "InvoiceApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CE94DC032E62CABD00C0649F;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				CE94DC362E62CB5000C0649F /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = CE94DC0D2E62CABD00C0649F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CE94DC0B2E62CABD00C0649F /* InvoiceApp */,
				CE94DC182E62CABF00C0649F /* InvoiceAppTests */,
				CE94DC222E62CABF00C0649F /* InvoiceAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CE94DC0A2E62CABD00C0649F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC172E62CABF00C0649F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC212E62CABF00C0649F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CE94DC082E62CABD00C0649F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC152E62CABF00C0649F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE94DC1F2E62CABF00C0649F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		CE94DC1B2E62CABF00C0649F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CE94DC0B2E62CABD00C0649F /* InvoiceApp */;
			targetProxy = CE94DC1A2E62CABF00C0649F /* PBXContainerItemProxy */;
		};
		CE94DC252E62CABF00C0649F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CE94DC0B2E62CABD00C0649F /* InvoiceApp */;
			targetProxy = CE94DC242E62CABF00C0649F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		CE94DC2B2E62CABF00C0649F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CE94DC2C2E62CABF00C0649F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CE94DC2E2E62CABF00C0649F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CE94DC2F2E62CABF00C0649F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		CE94DC312E62CABF00C0649F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/InvoiceApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/InvoiceApp";
			};
			name = Debug;
		};
		CE94DC322E62CABF00C0649F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/InvoiceApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/InvoiceApp";
			};
			name = Release;
		};
		CE94DC342E62CABF00C0649F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = InvoiceApp;
			};
			name = Debug;
		};
		CE94DC352E62CABF00C0649F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "-37611057.InvoiceAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = InvoiceApp;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CE94DC072E62CABD00C0649F /* Build configuration list for PBXProject "InvoiceApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE94DC2B2E62CABF00C0649F /* Debug */,
				CE94DC2C2E62CABF00C0649F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE94DC2D2E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE94DC2E2E62CABF00C0649F /* Debug */,
				CE94DC2F2E62CABF00C0649F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE94DC302E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE94DC312E62CABF00C0649F /* Debug */,
				CE94DC322E62CABF00C0649F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE94DC332E62CABF00C0649F /* Build configuration list for PBXNativeTarget "InvoiceAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE94DC342E62CABF00C0649F /* Debug */,
				CE94DC352E62CABF00C0649F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		CE94DC362E62CB5000C0649F /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */
	};
	rootObject = CE94DC042E62CABD00C0649F /* Project object */;
}
