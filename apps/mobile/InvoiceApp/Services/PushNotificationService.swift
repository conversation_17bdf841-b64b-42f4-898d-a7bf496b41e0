//
//  PushNotificationService.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation
import UserNotifications
import UIKit

// MARK: - Push Notification Service

@MainActor
class PushNotificationService: NSObject, ObservableObject {
    static let shared = PushNotificationService()
    
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    @Published var deviceToken: String?
    @Published var isRegistered = false
    
    private let supabaseService = SupabaseService.shared
    private let userNotificationCenter = UNUserNotificationCenter.current()
    
    override init() {
        super.init()
        userNotificationCenter.delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestPermission() async -> Bool {
        do {
            let granted = try await userNotificationCenter.requestAuthorization(
                options: [.alert, .badge, .sound, .provisional]
            )
            
            await MainActor.run {
                authorizationStatus = granted ? .authorized : .denied
            }
            
            if granted {
                await registerForRemoteNotifications()
            }
            
            Logger.shared.info("Push notification permission: \(granted ? "granted" : "denied")")
            return granted
        } catch {
            Logger.shared.error("Failed to request push notification permission: \(error)")
            return false
        }
    }
    
    private func checkAuthorizationStatus() {
        Task {
            let settings = await userNotificationCenter.notificationSettings()
            await MainActor.run {
                authorizationStatus = settings.authorizationStatus
            }
        }
    }
    
    private func registerForRemoteNotifications() async {
        await UIApplication.shared.registerForRemoteNotifications()
    }
    
    // MARK: - Device Token Management
    
    func setDeviceToken(_ deviceToken: Data) {
        let tokenString = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        self.deviceToken = tokenString
        
        Logger.shared.info("Device token received: \(tokenString)")
        
        // Register token with Supabase
        Task {
            await registerTokenWithBackend(tokenString)
        }
    }
    
    func handleRegistrationError(_ error: Error) {
        Logger.shared.error("Push notification registration failed: \(error)")
        isRegistered = false
    }
    
    private func registerTokenWithBackend(_ token: String) async {
        do {
            // Call the push-token-register edge function
            let response = try await supabaseService.client.functions.invoke(
                "push-token-register",
                options: FunctionInvokeOptions(
                    body: [
                        "device_token": token,
                        "platform": "ios",
                        "app_version": EnvironmentManager.shared.appVersion,
                        "device_model": DeviceInfo.modelName,
                        "system_version": DeviceInfo.systemVersion
                    ]
                )
            )
            
            isRegistered = true
            Logger.shared.info("Device token registered with backend successfully")
            
        } catch {
            Logger.shared.error("Failed to register device token with backend: \(error)")
            isRegistered = false
        }
    }
    
    // MARK: - Local Notifications
    
    func scheduleLocalNotification(
        title: String,
        body: String,
        identifier: String,
        timeInterval: TimeInterval = 0,
        userInfo: [String: Any] = [:]
    ) async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.userInfo = userInfo
        
        let trigger = timeInterval > 0 ? 
            UNTimeIntervalNotificationTrigger(timeInterval: timeInterval, repeats: false) : nil
        
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )
        
        do {
            try await userNotificationCenter.add(request)
            Logger.shared.info("Local notification scheduled: \(identifier)")
        } catch {
            Logger.shared.error("Failed to schedule local notification: \(error)")
        }
    }
    
    // MARK: - Notification Categories and Actions
    
    func setupNotificationCategories() {
        // Expense approval actions
        let approveAction = UNNotificationAction(
            identifier: "APPROVE_EXPENSE",
            title: "אשר",
            options: [.foreground]
        )
        
        let rejectAction = UNNotificationAction(
            identifier: "REJECT_EXPENSE",
            title: "דחה",
            options: [.foreground]
        )
        
        let expenseCategory = UNNotificationCategory(
            identifier: "EXPENSE_APPROVAL",
            actions: [approveAction, rejectAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Invoice reminder actions
        let viewInvoiceAction = UNNotificationAction(
            identifier: "VIEW_INVOICE",
            title: "הצג חשבונית",
            options: [.foreground]
        )
        
        let sendReminderAction = UNNotificationAction(
            identifier: "SEND_REMINDER",
            title: "שלח תזכורת",
            options: []
        )
        
        let invoiceCategory = UNNotificationCategory(
            identifier: "INVOICE_REMINDER",
            actions: [viewInvoiceAction, sendReminderAction],
            intentIdentifiers: [],
            options: []
        )
        
        // VAT reminder actions
        let viewReportAction = UNNotificationAction(
            identifier: "VIEW_VAT_REPORT",
            title: "הצג דוח",
            options: [.foreground]
        )
        
        let vatCategory = UNNotificationCategory(
            identifier: "VAT_REMINDER",
            actions: [viewReportAction],
            intentIdentifiers: [],
            options: []
        )
        
        userNotificationCenter.setNotificationCategories([
            expenseCategory,
            invoiceCategory,
            vatCategory
        ])
        
        Logger.shared.info("Notification categories configured")
    }
    
    // MARK: - Badge Management
    
    func updateBadgeCount(_ count: Int) {
        UIApplication.shared.applicationIconBadgeNumber = count
        Logger.shared.debug("Badge count updated to: \(count)")
    }
    
    func clearBadge() {
        updateBadgeCount(0)
    }
    
    // MARK: - Notification Handling
    
    func handleNotificationResponse(_ response: UNNotificationResponse) {
        let userInfo = response.notification.request.content.userInfo
        let actionIdentifier = response.actionIdentifier
        
        Logger.shared.info("Notification action: \(actionIdentifier)")
        
        switch actionIdentifier {
        case "APPROVE_EXPENSE":
            handleExpenseApproval(userInfo: userInfo, approved: true)
        case "REJECT_EXPENSE":
            handleExpenseApproval(userInfo: userInfo, approved: false)
        case "VIEW_INVOICE":
            handleViewInvoice(userInfo: userInfo)
        case "SEND_REMINDER":
            handleSendReminder(userInfo: userInfo)
        case "VIEW_VAT_REPORT":
            handleViewVATReport(userInfo: userInfo)
        case UNNotificationDefaultActionIdentifier:
            handleDefaultAction(userInfo: userInfo)
        default:
            break
        }
    }
    
    private func handleExpenseApproval(userInfo: [AnyHashable: Any], approved: Bool) {
        guard let expenseIdString = userInfo["expense_id"] as? String,
              let expenseId = UUID(uuidString: expenseIdString) else {
            Logger.shared.error("Invalid expense ID in notification")
            return
        }
        
        Task {
            do {
                let status: ExpenseStatus = approved ? .approved : .rejected
                _ = try await supabaseService.updateExpenseStatus(
                    expenseId: expenseId,
                    status: status
                )
                
                let message = approved ? "הוצאה אושרה" : "הוצאה נדחתה"
                await scheduleLocalNotification(
                    title: "עדכון הוצאה",
                    body: message,
                    identifier: "expense_update_\(expenseId)"
                )
                
            } catch {
                Logger.shared.error("Failed to update expense status: \(error)")
            }
        }
    }
    
    private func handleViewInvoice(userInfo: [AnyHashable: Any]) {
        guard let invoiceIdString = userInfo["invoice_id"] as? String,
              let invoiceId = UUID(uuidString: invoiceIdString) else {
            Logger.shared.error("Invalid invoice ID in notification")
            return
        }
        
        // Post notification to navigate to invoice
        NotificationCenter.default.post(
            name: .navigateToInvoice,
            object: nil,
            userInfo: ["invoice_id": invoiceId]
        )
    }
    
    private func handleSendReminder(userInfo: [AnyHashable: Any]) {
        guard let invoiceIdString = userInfo["invoice_id"] as? String,
              let invoiceId = UUID(uuidString: invoiceIdString) else {
            Logger.shared.error("Invalid invoice ID in notification")
            return
        }
        
        Task {
            do {
                // Call send-invoice-reminder edge function
                _ = try await supabaseService.client.functions.invoke(
                    "send-invoice-reminder",
                    options: FunctionInvokeOptions(
                        body: ["invoice_id": invoiceId.uuidString]
                    )
                )
                
                await scheduleLocalNotification(
                    title: "תזכורת נשלחה",
                    body: "תזכורת לתשלום החשבונית נשלחה ללקוח",
                    identifier: "reminder_sent_\(invoiceId)"
                )
                
            } catch {
                Logger.shared.error("Failed to send invoice reminder: \(error)")
            }
        }
    }
    
    private func handleViewVATReport(userInfo: [AnyHashable: Any]) {
        // Post notification to navigate to VAT report
        NotificationCenter.default.post(
            name: .navigateToVATReport,
            object: nil,
            userInfo: userInfo
        )
    }
    
    private func handleDefaultAction(userInfo: [AnyHashable: Any]) {
        // Handle default tap action
        if let screen = userInfo["screen"] as? String {
            NotificationCenter.default.post(
                name: .navigateToScreen,
                object: nil,
                userInfo: ["screen": screen]
            )
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension PushNotificationService: UNUserNotificationCenterDelegate {
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        handleNotificationResponse(response)
        completionHandler()
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let navigateToInvoice = Notification.Name("navigateToInvoice")
    static let navigateToVATReport = Notification.Name("navigateToVATReport")
    static let navigateToScreen = Notification.Name("navigateToScreen")
    static let expenseStatusUpdated = Notification.Name("expenseStatusUpdated")
}

// MARK: - Push Notification Types

enum PushNotificationType: String, CaseIterable {
    case expenseApproval = "expense_approval"
    case invoiceOverdue = "invoice_overdue"
    case vatReminder = "vat_reminder"
    case paymentReceived = "payment_received"
    case systemUpdate = "system_update"
    case documentShared = "document_shared"
    
    var category: String {
        switch self {
        case .expenseApproval:
            return "EXPENSE_APPROVAL"
        case .invoiceOverdue:
            return "INVOICE_REMINDER"
        case .vatReminder:
            return "VAT_REMINDER"
        default:
            return "DEFAULT"
        }
    }
    
    var title: String {
        switch self {
        case .expenseApproval:
            return "הוצאה חדשה לאישור"
        case .invoiceOverdue:
            return "חשבונית באיחור"
        case .vatReminder:
            return "תזכורת מע״מ"
        case .paymentReceived:
            return "תשלום התקבל"
        case .systemUpdate:
            return "עדכון מערכת"
        case .documentShared:
            return "מסמך שותף"
        }
    }
}
