//
//  SupabaseService.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import Foundation
import Supabase
import Combine

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()
    
    private let client: SupabaseClient
    private let authStateSubject = PassthroughSubject<Session?, Never>()
    
    var authStatePublisher: AnyPublisher<Session?, Never> {
        authStateSubject.eraseToAnyPublisher()
    }
    
    private init() {
        // Initialize Supabase client with actual project credentials
        let supabaseURL = URL(string: "https://zhwqtgypoueykwgphmqn.supabase.co")!
        let supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpod3F0Z3lwb3VleWt3Z3BobXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY0NjgyOTQsImV4cCI6MjA3MjA0NDI5NH0.OO6IgJ4oZQvP0T0lJd5OUgN2_apHT9xqpGhZbHXy0lM"
        
        self.client = SupabaseClient(
            supabaseURL: supabaseURL,
            supabaseKey: supabase<PERSON><PERSON>
        )
        
        setupAuthStateListener()
    }
    
    private func setupAuthStateListener() {
        Task {
            for await state in client.auth.authStateChanges {
                await MainActor.run {
                    authStateSubject.send(state.session)
                }
            }
        }
    }
    
    // MARK: - Authentication Methods
    
    func getCurrentSession() async throws -> Session? {
        return try await client.auth.session
    }
    
    func signIn(email: String, password: String) async throws -> Session {
        let response = try await client.auth.signIn(email: email, password: password)
        return response.session
    }
    
    func signUp(registrationData: RegistrationData) async throws -> (user: User, company: Company) {
        // Call the registration edge function
        let requestBody: [String: Any] = [
            "email": registrationData.email,
            "password": registrationData.password,
            "full_name": registrationData.fullName,
            "phone": registrationData.phone,
            "company": [
                "business_number": registrationData.company.businessNumber,
                "name_hebrew": registrationData.company.nameHebrew,
                "name_english": registrationData.company.nameEnglish ?? "",
                "address_hebrew": registrationData.company.addressHebrew,
                "city_hebrew": registrationData.company.cityHebrew,
                "phone": registrationData.company.phone,
                "industry": registrationData.company.industry,
                "annual_revenue": registrationData.company.annualRevenue ?? "",
                "interested_in_loan": registrationData.company.interestedInLoan,
                "interested_in_insurance": registrationData.company.interestedInInsurance,
                "interested_in_accounting": registrationData.company.interestedInAccounting
            ]
        ]
        
        let response: RegistrationResponse = try await client.functions.invoke(
            "auth-register",
            options: FunctionInvokeOptions(
                body: requestBody
            )
        )
        
        return (user: response.user, company: response.company)
    }
    
    func signOut() async throws {
        try await client.auth.signOut()
    }
    
    func getUserData(userId: UUID) async throws -> UserDataResponse {
        // Fetch user data
        let userResponse: User = try await client
            .from("users")
            .select()
            .eq("id", value: userId)
            .single()
            .execute()
            .value
        
        // Fetch user's companies
        let companiesResponse: [CompanyUserJoin] = try await client
            .from("company_users")
            .select("""
                company:companies(*)
            """)
            .eq("user_id", value: userId)
            .execute()
            .value
        
        let companies = companiesResponse.compactMap { $0.company }
        
        return UserDataResponse(user: userResponse, companies: companies)
    }
    
    // MARK: - Document Methods
    
    func getDocuments(companyId: UUID, limit: Int = 20) async throws -> [Document] {
        return try await client
            .from("documents")
            .select("""
                *,
                customer:customers(*),
                items:document_items(*)
            """)
            .eq("company_id", value: companyId)
            .order("created_at", ascending: false)
            .limit(limit)
            .execute()
            .value
    }
    
    func createDocument(_ document: CreateDocumentRequest) async throws -> Document {
        let response: Document = try await client.functions.invoke(
            "process-document",
            options: FunctionInvokeOptions(body: document)
        )
        return response
    }
    
    // MARK: - Customer Methods
    
    func getCustomers(companyId: UUID) async throws -> [Customer] {
        return try await client
            .from("customers")
            .select()
            .eq("company_id", value: companyId)
            .order("name_hebrew", ascending: true)
            .execute()
            .value
    }
    
    func createCustomer(_ customer: CreateCustomerRequest) async throws -> Customer {
        return try await client
            .from("customers")
            .insert(customer)
            .select()
            .single()
            .execute()
            .value
    }
    
    // MARK: - Expense Methods
    
    func getExpenses(companyId: UUID, status: ExpenseStatus? = nil) async throws -> [Expense] {
        var query = client
            .from("expenses")
            .select()
            .eq("company_id", value: companyId)
            .order("expense_date", ascending: false)
        
        if let status = status {
            query = query.eq("status", value: status.rawValue)
        }
        
        return try await query.execute().value
    }
    
    func updateExpenseStatus(expenseId: UUID, status: ExpenseStatus, rejectionReason: String? = nil) async throws -> Expense {
        var updateData: [String: Any] = ["status": status.rawValue]
        if let reason = rejectionReason {
            updateData["rejection_reason"] = reason
        }
        
        return try await client
            .from("expenses")
            .update(updateData)
            .eq("id", value: expenseId)
            .select()
            .single()
            .execute()
            .value
    }
    
    // MARK: - Dashboard Methods
    
    func getDashboardStats(companyId: UUID) async throws -> DashboardStats {
        // This would typically be implemented as a database function or view
        // For now, we'll make multiple queries
        
        let currentMonth = Calendar.current.dateInterval(of: .month, for: Date())!
        
        // Monthly revenue
        let monthlyRevenue: [RevenueResult] = try await client
            .from("documents")
            .select("total_amount")
            .eq("company_id", value: companyId)
            .eq("status", value: "approved")
            .gte("issue_date", value: currentMonth.start)
            .lte("issue_date", value: currentMonth.end)
            .execute()
            .value
        
        let totalRevenue = monthlyRevenue.reduce(0) { $0 + $1.totalAmount }
        
        // Pending expenses count
        let pendingExpenses: [ExpenseCount] = try await client
            .from("expenses")
            .select("id")
            .eq("company_id", value: companyId)
            .eq("status", value: "pending")
            .execute()
            .value
        
        return DashboardStats(
            monthlyRevenue: totalRevenue,
            pendingExpensesCount: pendingExpenses.count,
            vatLiability: 0, // Would be calculated
            nextPaymentDate: Date() // Would be calculated
        )
    }

    // MARK: - Notification Preferences

    func getNotificationPreferences(userId: UUID) async throws -> NotificationPreferences {
        let response: NotificationPreferencesResponse = try await client
            .from("notification_preferences")
            .select("*")
            .eq("user_id", value: userId.uuidString)
            .single()
            .execute()
            .value

        return NotificationPreferences(
            userId: userId,
            expenseApprovals: response.expenseApprovals,
            invoiceReminders: response.invoiceReminders,
            vatReminders: response.vatReminders,
            paymentNotifications: response.paymentNotifications,
            systemUpdates: response.systemUpdates,
            marketingNotifications: response.marketingNotifications
        )
    }

    func updateNotificationPreferences(_ preferences: NotificationPreferences) async throws {
        let data: [String: Any] = [
            "user_id": preferences.userId.uuidString,
            "expense_approvals": preferences.expenseApprovals,
            "invoice_reminders": preferences.invoiceReminders,
            "vat_reminders": preferences.vatReminders,
            "payment_notifications": preferences.paymentNotifications,
            "system_updates": preferences.systemUpdates,
            "marketing_notifications": preferences.marketingNotifications,
            "updated_at": ISO8601DateFormatter().string(from: Date())
        ]

        _ = try await client
            .from("notification_preferences")
            .upsert(data)
            .execute()
    }

    // MARK: - Push Notifications

    func sendPushNotification(
        userId: UUID? = nil,
        companyId: UUID? = nil,
        title: String,
        body: String,
        data: [String: Any]? = nil,
        category: String? = nil
    ) async throws {
        var payload: [String: Any] = [
            "title": title,
            "body": body
        ]

        if let userId = userId {
            payload["user_id"] = userId.uuidString
        }

        if let companyId = companyId {
            payload["company_id"] = companyId.uuidString
        }

        if let data = data {
            payload["data"] = data
        }

        if let category = category {
            payload["category"] = category
        }

        _ = try await client.functions.invoke(
            "send-push-notification",
            options: FunctionInvokeOptions(body: payload)
        )
    }
}

// MARK: - Response Models

struct RegistrationResponse: Codable {
    let user: User
    let company: Company
}

struct CompanyUserJoin: Codable {
    let company: Company?
}

struct RevenueResult: Codable {
    let totalAmount: Double
    
    enum CodingKeys: String, CodingKey {
        case totalAmount = "total_amount"
    }
}

struct ExpenseCount: Codable {
    let id: UUID
}

struct DashboardStats {
    let monthlyRevenue: Double
    let pendingExpensesCount: Int
    let vatLiability: Double
    let nextPaymentDate: Date
}

// MARK: - Request Models

struct CreateDocumentRequest: Codable {
    let companyId: UUID
    let documentType: String
    let customerId: UUID
    let issueDate: String
    let dueDate: String?
    let currency: String
    let items: [DocumentItemRequest]
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case documentType = "document_type"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case items
        case notes
    }
}

struct DocumentItemRequest: Codable {
    let productId: UUID?
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let vatRate: Double
    let discountPercent: Double?
    
    enum CodingKeys: String, CodingKey {
        case productId = "product_id"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case vatRate = "vat_rate"
        case discountPercent = "discount_percent"
    }
}

struct CreateCustomerRequest: Codable {
    let companyId: UUID
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String
    let cityHebrew: String
    let contactEmail: String?
    let contactPhone: String?

    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case cityHebrew = "city_hebrew"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
    }
}

struct NotificationPreferencesResponse: Codable {
    let userId: String
    let expenseApprovals: Bool
    let invoiceReminders: Bool
    let vatReminders: Bool
    let paymentNotifications: Bool
    let systemUpdates: Bool
    let marketingNotifications: Bool

    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case expenseApprovals = "expense_approvals"
        case invoiceReminders = "invoice_reminders"
        case vatReminders = "vat_reminders"
        case paymentNotifications = "payment_notifications"
        case systemUpdates = "system_updates"
        case marketingNotifications = "marketing_notifications"
    }
}
