//
//  InvoiceAppApp.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI
import UserNotifications

@main
struct InvoiceAppApp: App {
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var pushNotificationService = PushNotificationService.shared
    @StateObject private var appStoreCompliance = AppStoreComplianceManager.shared

    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authViewModel)
                .environmentObject(themeManager)
                .environmentObject(pushNotificationService)
                .environmentObject(appStoreCompliance)
                .preferredColorScheme(themeManager.colorScheme)
                .environment(\.layoutDirection, .rightToLeft) // RTL for Hebrew
                .onAppear {
                    setupApp()
                }
                .onReceive(NotificationCenter.default.publisher(for: .navigateToInvoice)) { notification in
                    handleDeepLink(notification)
                }
                .onReceive(NotificationCenter.default.publisher(for: .navigateToVATReport)) { notification in
                    handleDeepLink(notification)
                }
                .onReceive(NotificationCenter.default.publisher(for: .navigateToScreen)) { notification in
                    handleDeepLink(notification)
                }
                .sheet(isPresented: .constant(!appStoreCompliance.hasRequiredConsents)) {
                    PrivacyNoticeView()
                        .interactiveDismissDisabled()
                }
        }
    }

    private func setupApp() {
        // Record app launch for analytics
        appStoreCompliance.recordAppLaunch()

        // Setup push notifications
        setupPushNotifications()

        // Check authentication status
        Task {
            await authViewModel.checkAuthenticationStatus()
        }
    }

    private func setupPushNotifications() {
        // Setup notification categories
        pushNotificationService.setupNotificationCategories()

        // Request permission if user has consented
        if appStoreCompliance.hasRequiredConsents {
            Task {
                await pushNotificationService.requestPermission()
            }
        }
    }

    private func handleDeepLink(_ notification: Notification) {
        guard let userInfo = notification.userInfo else { return }

        // Handle navigation based on notification type
        switch notification.name {
        case .navigateToInvoice:
            if let invoiceId = userInfo["invoice_id"] as? UUID {
                Logger.shared.info("Navigating to invoice: \(invoiceId)")
            }
        case .navigateToVATReport:
            Logger.shared.info("Navigating to VAT report")
        case .navigateToScreen:
            if let screen = userInfo["screen"] as? String {
                Logger.shared.info("Navigating to screen: \(screen)")
            }
        default:
            break
        }
    }
}

// MARK: - App Delegate

class AppDelegate: NSObject, UIApplicationDelegate {

    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {

        // Configure crash reporting
        _ = CrashReporter.shared

        Logger.shared.info("App launched")

        return true
    }

    // MARK: - Push Notifications

    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        PushNotificationService.shared.setDeviceToken(deviceToken)
    }

    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        PushNotificationService.shared.handleRegistrationError(error)
    }

    // MARK: - Background App Refresh

    func application(
        _ application: UIApplication,
        performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        Task {
            Logger.shared.info("Performing background fetch")
            completionHandler(.newData)
        }
    }

    // MARK: - App State Changes

    func applicationDidBecomeActive(_ application: UIApplication) {
        PushNotificationService.shared.clearBadge()
        Logger.shared.info("App became active")
    }

    func applicationWillResignActive(_ application: UIApplication) {
        Logger.shared.info("App will resign active")
    }

    func applicationDidEnterBackground(_ application: UIApplication) {
        Logger.shared.info("App entered background")
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        Logger.shared.info("App will enter foreground")
    }
}
