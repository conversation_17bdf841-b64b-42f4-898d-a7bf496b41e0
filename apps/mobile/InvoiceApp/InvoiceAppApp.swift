//
//  InvoiceAppApp.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

@main
struct InvoiceAppApp: App {
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var themeManager = ThemeManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authViewModel)
                .environmentObject(themeManager)
                .preferredColorScheme(themeManager.colorScheme)
                .environment(\.layoutDirection, .rightToLeft) // RTL for Hebrew
        }
    }
}
