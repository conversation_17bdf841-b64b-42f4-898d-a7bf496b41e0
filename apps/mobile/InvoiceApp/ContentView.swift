//
//  ContentView.swift
//  InvoiceApp
//
//  Created by Development Team on 29/01/2025.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        Group {
            if authViewModel.isAuthenticated {
                MainTabView()
            } else {
                AuthenticationView()
            }
        }
        .onAppear {
            authViewModel.checkAuthenticationStatus()
        }
    }
}

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            DashboardView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("לוח בקרה")
                }
                .tag(0)
            
            DocumentsView()
                .tabItem {
                    Image(systemName: "doc.text.fill")
                    Text("מסמכים")
                }
                .tag(1)
            
            ExpensesView()
                .tabItem {
                    Image(systemName: "creditcard.fill")
                    Text("הוצאות")
                }
                .tag(2)
            
            ReportsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("דוחות")
                }
                .tag(3)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("הגדרות")
                }
                .tag(4)
        }
        .accentColor(Color.primary)
        .environment(\.layoutDirection, .rightToLeft)
    }
}

#Preview {
    ContentView()
        .environmentObject(AuthViewModel())
        .environmentObject(ThemeManager())
}
