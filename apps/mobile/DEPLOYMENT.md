# iOS Mobile App Deployment Guide

## 🚀 Complete Setup with Real Supabase Integration & Push Notifications

This guide covers deploying the iOS mobile app with the actual Supabase project and push notifications.

## 📋 Prerequisites

### Development Environment
- **Xcode 15.0+** with iOS 15.0+ deployment target
- **macOS 13.0+** (Ventura or later)
- **Apple Developer Account** (for push notifications and App Store)
- **Supabase Project** (already configured: `zhwqtgypoueykwgphmqn`)

### Required Certificates
- **iOS Development Certificate**
- **Push Notification Certificate** (APNs)
- **Provisioning Profiles** for development and distribution

## 🔧 Setup Instructions

### 1. Database Migration

First, apply the push notifications migration to your Supabase database:

```bash
cd supabase
npx supabase db push
```

This will create:
- `push_tokens` table for device tokens
- `notification_logs` table for tracking sent notifications
- `notification_preferences` table for user preferences
- Database functions for automated notifications

### 2. Supabase Edge Functions

Deploy the push notification functions:

```bash
# Deploy push token registration function
npx supabase functions deploy push-token-register

# Deploy push notification sender function
npx supabase functions deploy send-push-notification
```

### 3. iOS Project Configuration

#### A. Open in Xcode
```bash
cd apps/mobile
open InvoiceApp.xcodeproj
```

#### B. Configure Team & Bundle ID
1. Select the project in Xcode
2. Go to **Signing & Capabilities**
3. Set your **Team** (Apple Developer Account)
4. Update **Bundle Identifier**: `com.yourcompany.invoiceapp`

#### C. Add Push Notifications Capability
1. In **Signing & Capabilities**, click **+ Capability**
2. Add **Push Notifications**
3. Add **Background Modes** and enable:
   - Background fetch
   - Remote notifications
   - Background processing

#### D. Update Info.plist
The Info.plist is already configured with:
- Push notification permissions
- Camera/photo library access
- Hebrew localization
- Background modes

### 4. Push Notification Certificates

#### A. Create APNs Certificate
1. Go to [Apple Developer Portal](https://developer.apple.com)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create new **Apple Push Notification service SSL** certificate
4. Download and install in Keychain

#### B. Export Certificate
1. Open **Keychain Access**
2. Find your APNs certificate
3. Export as **.p12** file with password
4. Convert to **.pem** for server use:
```bash
openssl pkcs12 -in cert.p12 -out cert.pem -nodes
```

### 5. Environment Configuration

The app is already configured with the actual Supabase credentials:
- **URL**: `https://zhwqtgypoueykwgphmqn.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

For production, consider using environment-specific configurations.

## 📱 Testing Push Notifications

### 1. Local Testing
The app includes debug features for testing notifications:

```swift
// Test local notification
await pushNotificationService.scheduleLocalNotification(
    title: "Test Notification",
    body: "This is a test notification",
    identifier: "test_notification"
)
```

### 2. Backend Testing
Use the Supabase function to send test notifications:

```bash
curl -X POST 'https://zhwqtgypoueykwgphmqn.supabase.co/functions/v1/send-push-notification' \
  -H 'Authorization: Bearer YOUR_SERVICE_ROLE_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_id": "USER_UUID",
    "title": "Test Push Notification",
    "body": "This is a test from the backend",
    "category": "DEFAULT"
  }'
```

### 3. Automated Notifications
The system automatically sends notifications for:
- **New expenses** requiring approval
- **Overdue invoices** (checked daily)
- **VAT reminders** (7 days before deadline)
- **Payment confirmations**

## 🏪 App Store Preparation

### 1. App Store Connect Setup
1. Create new app in [App Store Connect](https://appstoreconnect.apple.com)
2. Configure app metadata:
   - **Name**: מערכת חשבוניות
   - **Category**: Business
   - **Content Rating**: 4+

### 2. Privacy Policy & Terms
The app includes built-in privacy policy and terms of service views. Update the content in:
- `AppStoreCompliance.swift`
- `PrivacyNoticeView.swift`

### 3. App Store Screenshots
Required screenshots for:
- iPhone 6.7" (iPhone 14 Pro Max)
- iPhone 6.5" (iPhone 11 Pro Max)
- iPhone 5.5" (iPhone 8 Plus)
- iPad Pro 12.9" (3rd generation)

### 4. App Store Description
```
מערכת ניהול חשבוניות והוצאות מתקדמת לעסקים קטנים ובינוניים.

תכונות עיקריות:
• יצירת חשבוניות מס וקבלות
• ניהול לקוחות וספקים  
• סריקת הוצאות באמצעות המצלמה
• דוחות מע״מ אוטומטיים
• התראות חכמות על הוצאות ותשלומים
• סנכרון עם מערכות הנהלת חשבונות
• תמיכה מלאה בעברית ו-RTL

האפליקציה מיועדת לעסקים הרוצים לייעל את תהליכי החשבונאות ולחסוך זמן יקר.
```

## 🔒 Security & Privacy

### 1. Data Protection
- All network communication uses **TLS 1.2+**
- Sensitive data encrypted with **AES-256**
- Biometric authentication support
- Secure keychain storage for tokens

### 2. Privacy Compliance
- **GDPR compliant** with data export/deletion
- **No tracking** across other apps
- **Minimal data collection**
- **User consent** for all data processing

### 3. App Store Privacy Labels
Configure in App Store Connect:
- **Data Types Collected**: Contact info, user content, usage data
- **Data Use**: App functionality, analytics
- **Data Sharing**: None
- **Data Retention**: User-controlled

## 🚀 Deployment Steps

### 1. Development Build
```bash
# Build for device testing
xcodebuild -project InvoiceApp.xcodeproj \
  -scheme InvoiceApp \
  -destination 'platform=iOS,name=YOUR_DEVICE' \
  build
```

### 2. TestFlight Distribution
1. Archive the app in Xcode (**Product > Archive**)
2. Upload to App Store Connect
3. Add internal/external testers
4. Distribute for testing

### 3. App Store Release
1. Complete App Store Connect metadata
2. Submit for review
3. Respond to any review feedback
4. Release when approved

## 📊 Monitoring & Analytics

### 1. Push Notification Analytics
Monitor notification performance in the database:
```sql
SELECT 
  title,
  sent_count,
  total_tokens,
  created_at
FROM notification_logs
ORDER BY created_at DESC;
```

### 2. App Performance
- **Crash reporting** built-in (privacy-safe)
- **Performance monitoring** with custom metrics
- **User engagement** tracking (anonymized)

### 3. Business Metrics
Track key business metrics:
- User registration conversion
- Feature adoption rates
- Document creation frequency
- Expense approval times

## 🔧 Troubleshooting

### Common Issues

#### Push Notifications Not Working
1. **Check certificate**: Ensure APNs certificate is valid
2. **Verify permissions**: User must grant notification permission
3. **Check device token**: Verify token registration in database
4. **Test environment**: Ensure using correct APNs environment

#### App Store Rejection
1. **Privacy policy**: Ensure privacy policy is accessible
2. **Permissions**: All permission requests must be justified
3. **Content**: Ensure all content is appropriate
4. **Functionality**: All features must work as described

#### Supabase Connection Issues
1. **Check credentials**: Verify Supabase URL and keys
2. **Network**: Ensure device has internet connection
3. **RLS policies**: Verify Row Level Security is configured
4. **Edge functions**: Ensure functions are deployed

## 📞 Support

### Development Support
- **Documentation**: [Supabase Docs](https://supabase.com/docs)
- **iOS Development**: [Apple Developer](https://developer.apple.com)
- **Push Notifications**: [APNs Guide](https://developer.apple.com/documentation/usernotifications)

### Production Support
- **App Store**: [App Store Connect Help](https://help.apple.com/app-store-connect/)
- **Supabase**: [Supabase Support](https://supabase.com/support)
- **Emergency**: Contact development team

---

## ✅ Deployment Checklist

- [ ] Database migration applied
- [ ] Edge functions deployed
- [ ] iOS project configured
- [ ] Push certificates installed
- [ ] App tested on device
- [ ] Privacy policy updated
- [ ] App Store metadata complete
- [ ] Screenshots captured
- [ ] TestFlight testing complete
- [ ] App Store submission ready

The mobile app is now ready for production deployment with full push notification support and real Supabase integration!
