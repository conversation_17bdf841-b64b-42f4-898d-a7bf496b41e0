# ``RealFunctions``

A type that extends ``ElementaryFunctions`` with additional operations that
are primarily used with real numbers.

## Topics

### Exponential functions
- ``exp2(_:)``
- ``exp10(_:)``

### Logarithmetic functions
- ``log2(_:)``
- ``log10(_:)``

### Plane geometry
- ``atan2(y:x:)``
- ``hypot(_:_:)``

### Gamma function
- ``gamma(_:)``
- ``logGamma(_:)``
- ``signGamma(_:)``

### Error function
- ``erf(_:)``
- ``erfc(_:)``
